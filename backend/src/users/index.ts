import { DynamoDBClient, GetItemCommand } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, ScanCommand, GetCommand, PutCommand, UpdateCommand, DeleteCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client } from '@aws-sdk/client-s3';
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { sendNotification, createFollowNotification } from '/opt/nodejs/notification-service';
import { logProfileUpdated, logDisplayNameUpdated, logUsernameChanged, extractRequestMetadata } from '/opt/nodejs/user-history-service';
import { DisplayNameProvider, DisplayNameOption, LinkedAccount, DISPLAY_NAME_PROVIDERS, DEFAULT_PROVIDER, isValidProvider, formatDisplayName } from '/opt/nodejs/display-name-config';
import { createResponse, getUserIdFromContext } from '/opt/nodejs/lambda-helpers';
import { userAuthService } from '/opt/nodejs/user-auth-service';

// Configure AWS SDK v3 clients
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const s3 = new S3Client(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const USER_PROFILES_TABLE = process.env.USER_PROFILES_TABLE;
const FOLLOWS_TABLE = process.env.FOLLOWS_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const AVATARS_BUCKET = process.env.AVATARS_BUCKET;
const XBOX_ACCOUNTS_TABLE = process.env.XBOX_ACCOUNTS_TABLE;

// TypeScript interfaces
interface User {
    id: string;
    email: string;
    username?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string; // Always includes provider prefix like "[xbox]Gamertag123"
    xboxUserId?: string;
    xboxGamertag?: string;
    xboxUserHash?: string;
    xboxXstsToken?: string;
    xboxTokenExpiry?: string;
    lastUsernameChangeAt?: string; // ISO timestamp of last username change
    usernameChangeCount?: number; // Number of times username has been changed
    createdAt: string;
    updatedAt: string;
}

interface UserProfile {
    userId: string;
    bio?: string;
    avatarUrl?: string;
    location?: string;
    website?: string;
    displayName?: string; // Always includes provider prefix like "[xbox]Gamertag123"
    followersCount?: number;
    followingCount?: number;
    createdAt?: string;
    updatedAt: string;
}

interface UserStats {
    postsCount: number;
    likesCount: number;
    followersCount: number;
    followingCount: number;
}

interface ChangeUsernameRequest {
    username: string;
}

// Username validation constants
const USERNAME_MIN_LENGTH = 4;
const USERNAME_MAX_LENGTH = 32;
const USERNAME_REGEX = /^[a-zA-Z0-9_-]+$/;

// Username change limitations
const USERNAME_CHANGE_COOLDOWN_DAYS = 30; // Once per month
const USERNAME_CHANGE_MAX_PER_YEAR = 5; // 5 times per year

// Common legitimate words that contain potential bad word substrings
const legitimateWords = new Set([
    'grass', 'class', 'classic', 'glass', 'pass', 'password', 'assessment', 'assist', 'assignment',
    'bass', 'mass', 'brass', 'compass', 'bypass', 'trespass', 'harass', 'embarrass',
    'hell', 'hello', 'shell', 'smell', 'spell', 'dwell', 'swell', 'well',
    'damn', 'damnation', // sometimes used in legitimate contexts
    'scrap', 'scrape', 'script', 'describe', 'prescription',
    'analyze', 'analysis', 'analyst',
]);

// Helper function to check if a username contains intentional profanity
const containsIntentionalProfanity = (username: string, badWordsList: string[]): boolean => {
    const lowerUsername = username.toLowerCase();

    // First check if the username (without numbers/symbols) is a known legitimate word
    const lettersOnly = lowerUsername.replace(/[^a-z]/g, '');
    if (legitimateWords.has(lettersOnly)) {
        return false;
    }

    for (const badWord of badWordsList) {
        const lowerBadWord = badWord.toLowerCase();

        // Skip very short words that are likely to cause false positives
        if (lowerBadWord.length <= 2) {
            continue;
        }

        // For 3-character bad words, be more strict
        if (lowerBadWord.length === 3) {
            // Only flag if it's the entire username or a very significant part
            if (lowerUsername === lowerBadWord ||
                (lowerUsername.startsWith(lowerBadWord) && lowerUsername.length <= 5) ||
                (lowerUsername.endsWith(lowerBadWord) && lowerUsername.length <= 5)) {
                return true;
            }
            continue;
        }

        // For longer bad words (4+ chars), check if they appear as a significant portion
        if (lowerBadWord.length >= 4) {
            // Check if the bad word makes up a significant portion of the username
            const badWordRatio = lowerBadWord.length / lowerUsername.length;
            if (badWordRatio >= 0.6 && lowerUsername.includes(lowerBadWord)) {
                // Additional check: make sure it's not just a substring of a legitimate word
                const withoutBadWord = lowerUsername.replace(lowerBadWord, '');
                const remainingChars = withoutBadWord.replace(/[^a-z]/g, '');

                // If most remaining characters are just numbers/symbols, it's likely intentional profanity
                if (remainingChars.length <= 2) {
                    return true;
                }
            }
        }
    }

    return false;
};

// Helper function to check profanity
const checkProfanity = async (username: string): Promise<boolean> => {
    try {
        // Dynamically import the bad-words library
        const { Filter } = await import('bad-words');
        const badWordsFilter = new Filter();

        // Use the built-in isProfane method first (most reliable for exact matches)
        if (badWordsFilter.isProfane(username)) {
            return true;
        }

        // Get the list of bad words for more sophisticated checking
        const badWordsList: string[] = badWordsFilter.list || [];

        // Use our more sophisticated checking
        if (containsIntentionalProfanity(username, badWordsList)) {
            return true;
        }

        // Check normalized versions (remove separators and try again)
        const normalizedVariants = [
            username.replace(/[\s_.-]/g, ''), // Remove separators
            username.replace(/[0-9]/g, ''),   // Remove numbers
        ];

        for (const variant of normalizedVariants) {
            if (variant.length > 0 && variant !== username) {
                // Only check if the variant is significantly different and still meaningful
                if (variant.length >= 3 && badWordsFilter.isProfane(variant)) {
                    return true;
                }
            }
        }

        return false;
    } catch (error) {
        console.error('Error loading bad-words library:', error);
        // If the library fails to load, we'll skip profanity checking
        return false;
    }
};

interface Follow {
    id: string;
    followerId: string;
    followingId: string;
    createdAt: string;
}

interface Like {
    postId: string;
    userId: string;
    post_id: string;
    user_id: string;
    createdAt: string;
}

interface XboxAccount {
    id: string;
    userId: string;
    xboxUserId: string;
    gamertag: string;
    xstsToken: string;
    userHash: string;
    microsoftRefreshToken?: string;
    profilePictureUrl?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    tokenExpiresAt: string;
}


// Helper function to get linked accounts for a user
const getLinkedAccounts = async (userId: string): Promise<LinkedAccount[]> => {
    const linkedAccounts: LinkedAccount[] = [];

    try {
        // Get all active user auth accounts from UserAuths table
        const userAuths = await userAuthService.findUserAuthsByUserId({
            userId: userId,
            activeOnly: true
        });

        // Convert UserAuth records to LinkedAccount format
        for (const userAuth of userAuths) {
            // Skip email provider as it's not a "linked" account for display name purposes
            if (userAuth.provider === 'email') {
                continue;
            }

            // Map AuthProvider to DisplayNameProvider (skip unsupported providers)
            let displayNameProvider: string;
            switch (userAuth.provider) {
                case 'xbox':
                    displayNameProvider = 'xbox';
                    break;
                case 'apple':
                    displayNameProvider = 'apple';
                    break;
                case 'twitch':
                    displayNameProvider = 'twitch';
                    break;
                case 'kick':
                    displayNameProvider = 'kick';
                    break;
                case 'discord':
                    displayNameProvider = 'discord';
                    break;
                case 'google':
                    // Google is not in DisplayNameProvider list, skip it
                    continue;
                default:
                    // Skip unknown providers
                    continue;
            }

            // Only include accounts that have a username/displayName
            if (userAuth.username) {
                linkedAccounts.push({
                    type: displayNameProvider as Exclude<DisplayNameProvider, 'gf'>,
                    accountId: userAuth.providerUserId || userAuth.id,
                    displayName: userAuth.username,
                    profilePictureUrl: userAuth.providerData?.profilePictureUrl,
                    linkedAt: userAuth.createdAt
                });
            }
        }

    } catch (error) {
        console.error('Error getting linked accounts:', error);
        // Don't fail the entire request if linked accounts can't be retrieved
    }

    return linkedAccounts;
};

// Helper function to get available display name options for a user
const getDisplayNameOptions = async (userId: string): Promise<DisplayNameOption[]> => {
    const options: DisplayNameOption[] = [];

    try {
        // Get user's current username
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);
        const user = userResult.Item as User;

        // Add GameFlex username option if user has a username
        if (user?.username) {
            options.push({
                provider: DEFAULT_PROVIDER,
                displayName: user.username,
                formattedDisplayName: user.username,
                isAvailable: true
            });
        }

        // Get linked accounts and add them as options
        const linkedAccounts = await getLinkedAccounts(userId);

        for (const account of linkedAccounts) {
            const formattedName = formatDisplayName(account.displayName, account.type);
            options.push({
                provider: account.type,
                displayName: account.displayName,
                formattedDisplayName: formattedName,
                isAvailable: true
            });
        }

    } catch (error) {
        console.error('Error getting display name options:', error);
    }

    return options;
};

// formatDisplayName function is now imported from config

// Helper function to calculate user statistics
const calculateUserStats = async (userId: string): Promise<UserStats> => {
    try {
        console.log('calculateUserStats: Calculating stats for user ID:', userId);

        // Count posts by user
        const scanPostsCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            },
            Select: 'COUNT'
        });
        const postsResult = await dynamodb.send(scanPostsCommand);

        console.log('calculateUserStats: Found', postsResult.Count, 'posts for user', userId);

        // Count likes by user (using GSI)
        const queryLikesCommand = new QueryCommand({
            TableName: LIKES_TABLE,
            IndexName: 'userIdIndex',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const likesResult = await dynamodb.send(queryLikesCommand);

        console.log('calculateUserStats: Found', likesResult.Count, 'likes for user', userId);

        // Count followers
        const scanFollowersCommand = new ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'followingId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const followersResult = await dynamodb.send(scanFollowersCommand);

        // Count following
        const scanFollowingCommand = new ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'followerId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const followingResult = await dynamodb.send(scanFollowingCommand);

        const stats: UserStats = {
            postsCount: postsResult.Count || 0,
            likesCount: likesResult.Count || 0,
            followersCount: followersResult.Count || 0,
            followingCount: followingResult.Count || 0
        };

        console.log('calculateUserStats: Final stats for user', userId, ':', stats);

        return stats;
    } catch (error) {
        console.error('Error calculating user stats:', error);
        return {
            postsCount: 0,
            likesCount: 0,
            followersCount: 0,
            followingCount: 0
        };
    }
};

// Get user profile
const getProfile = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get user ID from path parameters
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        const userId = id;

        // Get user from Users table
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        // Get user profile from UserProfiles table
        const getProfileCommand = new GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId }
        });
        const profileResult = await dynamodb.send(getProfileCommand);

        const user = userResult.Item as User;
        const profile = (profileResult.Item as UserProfile) || {};

        // Calculate real-time statistics
        const stats = await calculateUserStats(userId);

        // Get linked accounts
        const linkedAccounts = await getLinkedAccounts(userId);

        // Get auth providers for this user
        const userAuths = await userAuthService.findUserAuthsByUserId({ userId, activeOnly: true });
        const authProviders = userAuths.map(auth => auth.provider);

        // Create display name - use custom display name if set, otherwise fallback to firstName/lastName or username
        const displayName = user.displayName
            ? user.displayName // displayName already includes provider prefix like "[xbox]Gamertag123"
            : user.firstName && user.lastName
                ? `${user.firstName} ${user.lastName}`
                : user.username || 'Unknown User';

        return createResponse(200, {
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                display_name: displayName,
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: stats.followersCount,
                followingCount: stats.followingCount,
                postsCount: stats.postsCount,
                likesCount: stats.likesCount,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            },
            profile: {
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || ''
            },
            stats: {
                posts: stats.postsCount,
                followers: stats.followersCount,
                following: stats.followingCount,
                likes: stats.likesCount
            },
            linkedAccounts: linkedAccounts,
            authProviders: authProviders
        });

    } catch (error) {
        console.error('GetProfile error:', error);
        return createResponse(500, { error: 'Failed to get profile', details: (error as Error).message });
    }
};

// Update user profile
const updateProfile = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { firstName, lastName, bio, location, website, avatarUrl } = JSON.parse(event.body);

        // Get user ID from path parameters and authorizer context
        const { id } = event.pathParameters || {};
        const authenticatedUserId = getUserIdFromContext(event);

        if (!authenticatedUserId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Ensure user can only update their own profile
        if (id !== authenticatedUserId) {
            return createResponse(403, { error: 'You can only update your own profile' });
        }

        const userId = authenticatedUserId;

        // Update Users table
        const userUpdateExpression: string[] = [];
        const userExpressionAttributeValues: Record<string, any> = {};

        if (firstName !== undefined) {
            userUpdateExpression.push('firstName = :firstName');
            userExpressionAttributeValues[':firstName'] = firstName;
        }

        if (lastName !== undefined) {
            userUpdateExpression.push('lastName = :lastName');
            userExpressionAttributeValues[':lastName'] = lastName;
        }

        userUpdateExpression.push('updatedAt = :updatedAt');
        userExpressionAttributeValues[':updatedAt'] = new Date().toISOString();

        if (userUpdateExpression.length > 1) { // More than just updatedAt
            const updateUserCommand = new UpdateCommand({
                TableName: USERS_TABLE,
                Key: { id: userId },
                UpdateExpression: `SET ${userUpdateExpression.join(', ')}`,
                ExpressionAttributeValues: userExpressionAttributeValues
            });
            await dynamodb.send(updateUserCommand);
        }

        // Update or create UserProfiles table entry
        const profileUpdateExpression: string[] = [];
        const profileExpressionAttributeValues: Record<string, any> = {};

        if (bio !== undefined) {
            profileUpdateExpression.push('bio = :bio');
            profileExpressionAttributeValues[':bio'] = bio;
        }

        if (location !== undefined) {
            profileUpdateExpression.push('#location = :location');
            profileExpressionAttributeValues[':location'] = location;
        }

        if (website !== undefined) {
            profileUpdateExpression.push('website = :website');
            profileExpressionAttributeValues[':website'] = website;
        }

        if (avatarUrl !== undefined) {
            profileUpdateExpression.push('avatarUrl = :avatarUrl');
            profileExpressionAttributeValues[':avatarUrl'] = avatarUrl;
        }

        profileUpdateExpression.push('updatedAt = :updatedAt');
        profileExpressionAttributeValues[':updatedAt'] = new Date().toISOString();

        if (profileUpdateExpression.length > 0) {
            const updateProfileCommand = new UpdateCommand({
                TableName: USER_PROFILES_TABLE,
                Key: { userId: userId },
                UpdateExpression: `SET ${profileUpdateExpression.join(', ')}`,
                ExpressionAttributeNames: location !== undefined ? { '#location': 'location' } : undefined,
                ExpressionAttributeValues: profileExpressionAttributeValues
            });
            await dynamodb.send(updateProfileCommand);
        }

        // Log profile update
        try {
            const changedFields: Record<string, any> = {};
            if (firstName !== undefined) changedFields.firstName = firstName;
            if (lastName !== undefined) changedFields.lastName = lastName;
            if (bio !== undefined) changedFields.bio = bio;
            if (location !== undefined) changedFields.location = location;
            if (website !== undefined) changedFields.website = website;
            if (avatarUrl !== undefined) changedFields.avatarUrl = avatarUrl;

            if (Object.keys(changedFields).length > 0) {
                const { ipAddress, userAgent } = extractRequestMetadata(event);
                await logProfileUpdated(userId, { after: changedFields }, ipAddress, userAgent);
            }
        } catch (historyError) {
            console.error('Failed to log profile update:', historyError);
            // Don't fail the request if history logging fails
        }

        return createResponse(200, { message: 'Profile updated successfully' });

    } catch (error) {
        console.error('UpdateProfile error:', error);
        return createResponse(500, { error: 'Failed to update profile', details: (error as Error).message });
    }
};

// Get user by ID
const getUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const getProfileCommand = new GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id }
        });
        const profileResult = await dynamodb.send(getProfileCommand);

        const user = userResult.Item as User;
        const profile = (profileResult.Item as UserProfile) || {};

        // Calculate real-time statistics
        const stats = await calculateUserStats(id);

        // Get linked accounts
        const linkedAccounts = await getLinkedAccounts(id);

        // Get auth providers for this user
        const userAuths = await userAuthService.findUserAuthsByUserId({ userId: id, activeOnly: true });
        const authProviders = userAuths.map(auth => auth.provider);

        // Check if current user is following this user
        const currentUserId = getUserIdFromContext(event);
        let isFollowing = false;
        if (currentUserId && currentUserId !== id) {
            const getFollowCommand = new GetCommand({
                TableName: FOLLOWS_TABLE,
                Key: { followerId: currentUserId, followingId: id }
            });
            const followResult = await dynamodb.send(getFollowCommand);
            isFollowing = !!followResult.Item;
        }

        // Create display name - use custom display name if set, otherwise fallback to firstName/lastName or username
        const displayName = user.displayName
            ? user.displayName // displayName already includes provider prefix like "[xbox]Gamertag123"
            : user.firstName && user.lastName
                ? `${user.firstName} ${user.lastName}`
                : user.username || 'Unknown User';

        return createResponse(200, {
            user: {
                id: user.id,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                display_name: displayName,
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: stats.followersCount,
                followingCount: stats.followingCount,
                postsCount: stats.postsCount,
                likesCount: stats.likesCount,
                createdAt: user.createdAt
            },
            profile: {
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || ''
            },
            stats: {
                posts: stats.postsCount,
                followers: stats.followersCount,
                following: stats.followingCount,
                likes: stats.likesCount
            },
            linkedAccounts: linkedAccounts,
            authProviders: authProviders,
            isFollowing: isFollowing
        });

    } catch (error) {
        console.error('GetUser error:', error);
        return createResponse(500, { error: 'Failed to get user', details: (error as Error).message });
    }
};

// Follow user
const followUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {}; // user to follow

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (userId === id) {
            return createResponse(400, { error: 'Cannot follow yourself' });
        }

        // Check if already following
        const getFollowCommand = new GetCommand({
            TableName: FOLLOWS_TABLE,
            Key: { followerId: userId, followingId: id }
        });
        const existingFollow = await dynamodb.send(getFollowCommand);

        if (existingFollow.Item) {
            return createResponse(400, { error: 'Already following this user' });
        }

        // Add follow relationship
        const putFollowCommand = new PutCommand({
            TableName: FOLLOWS_TABLE,
            Item: {
                followerId: userId,
                followingId: id,
                createdAt: new Date().toISOString()
            }
        });
        await dynamodb.send(putFollowCommand);

        // Update follower's following count
        const updateFollowerCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId },
            UpdateExpression: 'ADD followingCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateFollowerCommand);

        // Update followed user's followers count
        const updateFollowedCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id },
            UpdateExpression: 'ADD followersCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateFollowedCommand);

        // Get follower user info for notification
        try {
            const followerUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const followerUserResult = await dynamodb.send(followerUserCommand);
            const followerUser = followerUserResult.Item;

            const followerProfileCommand = new GetCommand({
                TableName: USER_PROFILES_TABLE,
                Key: { userId: userId }
            });
            const followerProfileResult = await dynamodb.send(followerProfileCommand);
            const followerProfile = followerProfileResult.Item;

            if (followerUser) {
                // Create display name from firstName and lastName, fallback to username
                const followerDisplayName = followerUser.firstName && followerUser.lastName
                    ? `${followerUser.firstName} ${followerUser.lastName}`
                    : followerUser.username || 'Unknown User';

                // Send follow notification
                const notification = createFollowNotification(
                    id, // recipient (user being followed)
                    userId, // actor (user doing the following)
                    followerUser.username || 'Unknown User',
                    followerDisplayName,
                    followerProfile?.avatarUrl
                );

                // Send notification asynchronously (don't wait for it to complete)
                sendNotification(notification).catch(error => {
                    console.error('Failed to send follow notification:', error);
                    // Don't fail the follow operation if notification fails
                });

                console.log(`Follow notification queued for user ${id} from ${userId}`);
            }
        } catch (notificationError) {
            console.error('Error preparing follow notification:', notificationError);
            // Don't fail the follow operation if notification preparation fails
        }

        return createResponse(200, { message: 'User followed successfully' });

    } catch (error) {
        console.error('FollowUser error:', error);
        return createResponse(500, { error: 'Failed to follow user', details: (error as Error).message });
    }
};

// Unfollow user
const unfollowUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {}; // user to unfollow

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove follow relationship
        const deleteFollowCommand = new DeleteCommand({
            TableName: FOLLOWS_TABLE,
            Key: { followerId: userId, followingId: id }
        });
        await dynamodb.send(deleteFollowCommand);

        // Update follower's following count
        const updateFollowerCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId },
            UpdateExpression: 'ADD followingCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateFollowerCommand);

        // Update followed user's followers count
        const updateFollowedCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id },
            UpdateExpression: 'ADD followersCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateFollowedCommand);

        return createResponse(200, { message: 'User unfollowed successfully' });

    } catch (error) {
        console.error('UnfollowUser error:', error);
        return createResponse(500, { error: 'Failed to unfollow user', details: (error as Error).message });
    }
};

// Get user's posts by user ID
const getUserPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Get pagination parameters
        const { limit = '20', offset = '0' } = event.queryStringParameters || {};
        const limitNum = parseInt(limit);
        const offsetNum = parseInt(offset);

        // Get current user ID from authorizer context for like status
        const currentUserId = getUserIdFromContext(event);

        // Use scan with filter since we don't have a GSI yet
        // TODO: Replace with GSI query once userId-createdAt-index is added
        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'userId = :userId AND #status = :status AND #active = :active',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#active': 'active'
            },
            ExpressionAttributeValues: {
                ':userId': id,
                ':status': 'published',
                ':active': true
            }
        });

        const result = await dynamodb.send(scanCommand);
        const allPosts = result.Items || [];

        // Sort posts by createdAt descending
        const sortedPosts = allPosts.sort((a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        // Apply pagination
        const paginatedPosts = sortedPosts.slice(offsetNum, offsetNum + limitNum);

        // Enrich posts with user data, media, and like status
        const enrichedPosts = await Promise.all(paginatedPosts.map(async (post: any) => {
            try {
                // Get user data
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: post.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);
                const user = userResult.Item;

                // Get media data if mediaId exists
                let media = null;
                if (post.mediaId) {
                    const mediaId = post.mediaId;
                    const getMediaCommand = new GetCommand({
                        TableName: MEDIA_TABLE,
                        Key: { id: mediaId }
                    });
                    const mediaResult = await dynamodb.send(getMediaCommand);
                    media = mediaResult.Item;
                }

                // Check if current user liked this post
                let isLikedByCurrentUser = false;
                if (currentUserId) {
                    const getLikeCommand = new GetCommand({
                        TableName: LIKES_TABLE,
                        Key: { postId: post.id, userId: currentUserId }
                    });
                    const likeResult = await dynamodb.send(getLikeCommand);
                    isLikedByCurrentUser = !!likeResult.Item;
                }

                return {
                    ...post,
                    user: user ? {
                        id: user.id,
                        username: user.username,
                        display_name: user.displayName
                            ? user.displayName // displayName already includes provider prefix like "[xbox]Gamertag123"
                            : user.firstName && user.lastName
                                ? `${user.firstName} ${user.lastName}`
                                : user.username || 'Unknown User',
                        avatar_url: null // Would need to get from UserProfiles table if needed
                    } : null,
                    media: media ? {
                        id: media.id,
                        url: media.url,
                        type: media.type,
                        thumbnailUrl: media.thumbnailUrl
                    } : null,
                    isLikedByCurrentUser: isLikedByCurrentUser,
                    likeCount: post.likes || 0,
                    commentCount: post.comments || 0,
                    reflexCount: post.reflexes || 0
                };
            } catch (error) {
                console.error(`Error enriching post ${post.id}:`, error);
                return post; // Return original post if enrichment fails
            }
        }));

        return createResponse(200, {
            posts: enrichedPosts,
            count: enrichedPosts.length,
            hasMore: offsetNum + limitNum < sortedPosts.length
        });

    } catch (error) {
        console.error('GetUserPosts error:', error);
        return createResponse(500, { error: 'Failed to get user posts', details: (error as Error).message });
    }
};

// Get user's liked posts
const getUserLikedPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get user ID from path parameters
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        const userId = id;

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const limit = parseInt(queryParams.limit || '20') || 20;
        const lastEvaluatedKey = queryParams.lastEvaluatedKey ? JSON.parse(decodeURIComponent(queryParams.lastEvaluatedKey)) : undefined;

        // Debug: Log the user ID being used
        console.log('getUserLikedPosts: Looking for likes for user ID:', userId);
        console.log('getUserLikedPosts: User ID type:', typeof userId);
        console.log('getUserLikedPosts: LIKES_TABLE:', LIKES_TABLE);
        console.log('getUserLikedPosts: lastEvaluatedKey:', lastEvaluatedKey);

        // Build query parameters
        const queryParams_db: any = {
            TableName: LIKES_TABLE,
            IndexName: 'userIdIndex',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            ScanIndexForward: false, // Sort by most recent first
            Limit: limit
        };

        // Add pagination if provided
        if (lastEvaluatedKey) {
            queryParams_db.ExclusiveStartKey = lastEvaluatedKey;
        }

        // Get user's likes using GSI
        const queryLikesCommand = new QueryCommand(queryParams_db);
        const likesResult = await dynamodb.send(queryLikesCommand);

        console.log('getUserLikedPosts: Found', likesResult.Items?.length || 0, 'likes for user', userId);
        console.log('getUserLikedPosts: Raw likes result:', JSON.stringify(likesResult.Items, null, 2));

        const likes = likesResult.Items || [];

        // Get post details for each like
        const likedPosts = await Promise.all(likes.map(async (like: any) => {
            try {
                // Get post details
                console.log('getUserLikedPosts: Getting post details for post ID:', like.postId);
                console.log('getUserLikedPosts: POSTS_TABLE:', POSTS_TABLE);
                console.log('getUserLikedPosts: DynamoDB get params:', JSON.stringify({
                    TableName: POSTS_TABLE,
                    Key: { id: like.postId }
                }, null, 2));
                console.log('getUserLikedPosts: AWS region:', process.env.AWS_REGION);

                // Use raw DynamoDB client (DocumentClient has issues in this function)
                const getPostCommand = new GetItemCommand({
                    TableName: POSTS_TABLE,
                    Key: marshall({ id: like.postId })
                });
                const rawPostResult = await dynamodbClient.send(getPostCommand);

                console.log('getUserLikedPosts: Raw DynamoDB result:', JSON.stringify(rawPostResult, null, 2));

                // Convert raw result to DocumentClient format
                const postResult = {
                    Item: rawPostResult.Item ? unmarshall(rawPostResult.Item) : null
                };

                if (!postResult.Item) {
                    return null; // Post might have been deleted
                }

                const post = postResult.Item;

                // Get user details for the post author using raw DynamoDB client
                // Use authorId or userId field (posts have both)
                const authorId = post.authorId || post.userId;
                console.log('getUserLikedPosts: Author ID:', authorId);

                let user: any = {};
                if (authorId) {
                    try {
                        const getUserCommand = new GetItemCommand({
                            TableName: USERS_TABLE,
                            Key: marshall({ id: authorId })
                        });
                        const rawUserResult = await dynamodbClient.send(getUserCommand);
                        user = rawUserResult.Item ? unmarshall(rawUserResult.Item) : {};
                        console.log('getUserLikedPosts: User lookup result:', JSON.stringify(user, null, 2));
                    } catch (userError) {
                        console.error('getUserLikedPosts: Error getting user details:', userError);
                        user = {}; // Use empty user object if lookup fails
                    }
                } else {
                    console.log('getUserLikedPosts: No author ID found for post');
                }

                return {
                    id: like.postId,
                    userId: authorId,
                    content: post.content,
                    mediaUrl: post.media_url,
                    mediaType: post.media_type,
                    likeCount: post.likes || 0,
                    commentCount: post.comments || 0,
                    createdAt: post.createdAt,
                    likedAt: like.createdAt,
                    username: user.username,
                    displayName: user.displayName
                        ? formatDisplayName(user.displayName, user.displayNameProvider)
                        : user.firstName && user.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : user.username,
                    avatarUrl: null, // Would need to get from UserProfiles table if needed
                    isLikedByCurrentUser: true // Always true since these are the user's likes
                };
            } catch (error) {
                console.error(`Error getting post details for like ${like.postId}:`, error);
                return null;
            }
        }));

        // Filter out null results (deleted posts)
        const validLikedPosts = likedPosts.filter(post => post !== null);

        // Prepare response with proper pagination
        const response: any = {
            posts: validLikedPosts,
            count: validLikedPosts.length,
            hasMore: !!likesResult.LastEvaluatedKey
        };

        // Include pagination token if there are more results
        if (likesResult.LastEvaluatedKey) {
            response.lastEvaluatedKey = encodeURIComponent(JSON.stringify(likesResult.LastEvaluatedKey));
        }

        return createResponse(200, response);

    } catch (error) {
        console.error('GetUserLikedPosts error:', error);
        return createResponse(500, { error: 'Failed to get liked posts', details: (error as Error).message });
    }
};

// Get available display name options for a user
const getDisplayNameOptionsEndpoint = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get user ID from path parameters and authorizer context
        const { id } = event.pathParameters || {};
        const authenticatedUserId = getUserIdFromContext(event);

        if (!authenticatedUserId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Ensure user can only get their own display name options
        if (id !== authenticatedUserId) {
            return createResponse(403, { error: 'You can only get your own display name options' });
        }

        const options = await getDisplayNameOptions(id);

        return createResponse(200, {
            options
        });

    } catch (error) {
        console.error('getDisplayNameOptions error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};

// Update user's display name preference
const updateDisplayName = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { provider, displayName } = JSON.parse(event.body);

        // Get user ID from path parameters and authorizer context
        const { id } = event.pathParameters || {};
        const authenticatedUserId = getUserIdFromContext(event);

        if (!authenticatedUserId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Ensure user can only update their own display name
        if (id !== authenticatedUserId) {
            return createResponse(403, { error: 'You can only update your own display name' });
        }

        if (!provider || !displayName) {
            return createResponse(400, { error: 'Provider and displayName are required' });
        }

        // Validate provider
        if (!isValidProvider(provider)) {
            return createResponse(400, { error: 'Invalid provider' });
        }

        // If provider is not the default provider, verify the user has this linked account
        if (provider !== DEFAULT_PROVIDER) {
            const linkedAccounts = await getLinkedAccounts(id);
            const hasAccount = linkedAccounts.some(account =>
                account.type === provider && account.displayName === displayName
            );

            if (!hasAccount) {
                return createResponse(400, { error: 'You do not have this linked account' });
            }
        }

        const userId = authenticatedUserId;

        // Format the display name with provider prefix
        const formattedDisplayName = formatDisplayName(displayName, provider);

        // Update Users table (store formatted displayName with provider prefix)
        const updateUserCommand = new UpdateCommand({
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'SET displayName = :displayName, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':displayName': formattedDisplayName, // Store formatted displayName like "[xbox]Gamertag123"
                ':updatedAt': new Date().toISOString()
            }
        });
        await dynamodb.send(updateUserCommand);

        // Also update UserProfiles table if profile exists
        try {
            const updateProfileCommand = new UpdateCommand({
                TableName: USER_PROFILES_TABLE,
                Key: { userId: userId },
                UpdateExpression: 'SET displayName = :displayName, updatedAt = :updatedAt',
                ExpressionAttributeValues: {
                    ':displayName': formattedDisplayName, // Store formatted displayName like "[xbox]Gamertag123"
                    ':updatedAt': new Date().toISOString()
                }
            });
            await dynamodb.send(updateProfileCommand);
        } catch (profileError) {
            // Profile might not exist yet, that's okay
            console.log('Profile update failed (profile might not exist):', profileError);
        }

        return createResponse(200, {
            message: 'Display name updated successfully',
            displayName: formattedDisplayName, // Return formatted displayName like "[xbox]Gamertag123"
            provider
        });

    } catch (error) {
        console.error('updateDisplayName error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};

// Change username
const changeUsername = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { username: rawUsername }: ChangeUsernameRequest = JSON.parse(event.body);

        if (!rawUsername) {
            return createResponse(400, { error: 'Username is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        // Normalize username to lowercase for case-insensitive handling
        const username = rawUsername.toLowerCase();

        // Validate username length
        if (username.length < USERNAME_MIN_LENGTH || username.length > USERNAME_MAX_LENGTH) {
            return createResponse(400, {
                error: 'Invalid username length',
                details: `Username must be ${USERNAME_MIN_LENGTH}-${USERNAME_MAX_LENGTH} characters long`
            });
        }

        // Validate username format
        if (!USERNAME_REGEX.test(username)) {
            return createResponse(400, {
                error: 'Invalid username format',
                details: 'Username can only contain alphanumeric characters, underscores, and hyphens'
            });
        }

        // Check for profanity
        if (await checkProfanity(username)) {
            return createResponse(400, {
                error: 'Invalid username',
                details: 'Username contains inappropriate content. Please choose a different username.'
            });
        }

        // Get current user to verify they exist and check current username
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });

        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Item as User;

        // Check if the new username is the same as current username
        if (user.username === username) {
            return createResponse(400, {
                error: 'Same username',
                details: 'The new username is the same as your current username.'
            });
        }

        // Check username change limitations
        const now = new Date();
        const currentYear = now.getFullYear();

        // Check if user has changed username recently (cooldown period)
        if (user.lastUsernameChangeAt) {
            const lastChangeDate = new Date(user.lastUsernameChangeAt);
            const daysSinceLastChange = Math.floor((now.getTime() - lastChangeDate.getTime()) / (1000 * 60 * 60 * 24));

            if (daysSinceLastChange < USERNAME_CHANGE_COOLDOWN_DAYS) {
                const daysRemaining = USERNAME_CHANGE_COOLDOWN_DAYS - daysSinceLastChange;
                return createResponse(429, {
                    error: 'Username change cooldown',
                    details: `You can only change your username once per month. Please wait ${daysRemaining} more day(s).`
                });
            }
        }

        // Check yearly limit
        const usernameChangeCount = user.usernameChangeCount || 0;
        if (usernameChangeCount >= USERNAME_CHANGE_MAX_PER_YEAR) {
            return createResponse(429, {
                error: 'Username change limit exceeded',
                details: `You have reached the maximum of ${USERNAME_CHANGE_MAX_PER_YEAR} username changes per year.`
            });
        }

        // Check if username already exists (case-insensitive)
        const checkUsernameCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'UsernameIndex',
            KeyConditionExpression: 'username = :username',
            ExpressionAttributeValues: {
                ':username': username
            }
        });

        const existingUserResult = await dynamodb.send(checkUsernameCommand);

        if (existingUserResult.Items && existingUserResult.Items.length > 0) {
            return createResponse(409, {
                error: 'Username already taken',
                details: 'This username is already in use. Please choose a different one.'
            });
        }

        // Update user with new username and tracking fields
        const updateCommand = new UpdateCommand({
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'SET username = :username, lastUsernameChangeAt = :lastUsernameChangeAt, usernameChangeCount = :usernameChangeCount, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':username': username,
                ':lastUsernameChangeAt': now.toISOString(),
                ':usernameChangeCount': usernameChangeCount + 1,
                ':updatedAt': now.toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });

        const updateResult = await dynamodb.send(updateCommand);
        const updatedUser = updateResult.Attributes as User;

        // Log username change action
        try {
            const { ipAddress, userAgent } = extractRequestMetadata(event);
            await logUsernameChanged(userId, user.username || '', username, ipAddress, userAgent);
        } catch (historyError) {
            console.error('Failed to log username change:', historyError);
            // Don't fail the request if history logging fails
        }

        return createResponse(200, {
            message: 'Username changed successfully',
            user: {
                id: updatedUser.id,
                email: updatedUser.email,
                username: updatedUser.username,
                firstName: updatedUser.firstName,
                lastName: updatedUser.lastName
            },
            remainingChanges: USERNAME_CHANGE_MAX_PER_YEAR - (usernameChangeCount + 1),
            nextChangeAvailable: new Date(now.getTime() + (USERNAME_CHANGE_COOLDOWN_DAYS * 24 * 60 * 60 * 1000)).toISOString()
        });

    } catch (error: any) {
        console.error('ChangeUsername error:', error);

        // Handle specific DynamoDB errors
        if (error.name === 'ConditionalCheckFailedException') {
            return createResponse(409, {
                error: 'Username conflict',
                details: 'This username is already taken or there was a conflict updating your account.'
            });
        } else if (error.name === 'ValidationException') {
            return createResponse(400, {
                error: 'Invalid request',
                details: 'Please check your input and try again.'
            });
        } else {
            return createResponse(500, {
                error: 'Failed to change username',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'GET' && path.endsWith('/profile') && pathParameters && pathParameters.id) {
            return await getProfile(event);
        } else if (httpMethod === 'PUT' && path.endsWith('/profile') && pathParameters && pathParameters.id) {
            return await updateProfile(event);
        } else if (httpMethod === 'GET' && path.endsWith('/display-name-options') && pathParameters && pathParameters.id) {
            return await getDisplayNameOptionsEndpoint(event);
        } else if (httpMethod === 'PUT' && path.endsWith('/display-name') && pathParameters && pathParameters.id) {
            return await updateDisplayName(event);
        } else if (httpMethod === 'GET' && path.endsWith('/liked-posts') && pathParameters && pathParameters.id) {
            return await getUserLikedPosts(event);
        } else if (httpMethod === 'GET' && path.includes('/posts') && pathParameters && pathParameters.id && !path.includes('/liked-posts')) {
            return await getUserPosts(event);
        } else if (httpMethod === 'GET' && path.startsWith('/users/') && pathParameters && pathParameters.id && !path.includes('/posts') && !path.includes('/follow') && !path.includes('/profile') && !path.includes('/liked-posts') && !path.includes('/display-name')) {
            return await getUser(event);
        } else if (httpMethod === 'POST' && path.includes('/follow')) {
            return await followUser(event);
        } else if (httpMethod === 'DELETE' && path.includes('/follow')) {
            return await unfollowUser(event);
        } else if (httpMethod === 'PUT' && path === '/users/me/username') {
            return await changeUsername(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};
