import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import '../utils/app_logger.dart';

/// Service to handle all permission requests in a centralized way
class PermissionService {
  static PermissionService? _instance;
  static PermissionService get instance => _instance ??= PermissionService._();

  PermissionService._();

  /// Check and request camera permission
  Future<PermissionResult> requestCameraPermission() async {
    try {
      var status = await Permission.camera.status;
      AppLogger.debug('Camera permission status: $status');

      if (status.isGranted) {
        return PermissionResult.granted();
      }

      // On iOS, sometimes the permission system reports incorrect status in debug mode
      // If it's permanently denied but we haven't requested it yet, try requesting first
      if (Platform.isIOS && status.isPermanentlyDenied) {
        AppLogger.debug(
          'iOS: Permission shows permanently denied, but attempting request anyway (debug mode quirk)',
        );

        // Try to request permission anyway - iOS debug mode can be unreliable
        status = await Permission.camera.request();
        AppLogger.debug('Camera permission after request: $status');

        if (status.isGranted) {
          return PermissionResult.granted();
        }

        // If still permanently denied after request, it's truly denied
        if (status.isPermanentlyDenied) {
          return PermissionResult.permanentlyDenied(
            'Camera access is required to take photos. Please enable it in Settings.',
          );
        }

        // If it's just denied (not permanently), allow user to try again
        return PermissionResult.denied(
          'Camera access is required to take photos.',
        );
      }

      // For Android or iOS when not permanently denied, request normally
      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Camera access is required to take photos. Please enable it in Settings.',
        );
      }

      // Request permission
      status = await Permission.camera.request();
      AppLogger.debug('Camera permission after request: $status');

      if (status.isGranted) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Camera access is required to take photos. Please enable it in Settings.',
        );
      }

      return PermissionResult.denied(
        'Camera access is required to take photos.',
      );
    } catch (e) {
      AppLogger.error('Error requesting camera permission', error: e);
      return PermissionResult.error(
        'Unable to check camera permissions. Please try again.',
      );
    }
  }

  /// Check and request photo library permission
  Future<PermissionResult> requestPhotoLibraryPermission() async {
    try {
      PermissionStatus status;

      // Use appropriate permission based on platform and Android version
      if (Platform.isAndroid) {
        // Try photos permission first (Android 13+)
        status = await Permission.photos.status;
        if (status == PermissionStatus.denied) {
          // Fallback to storage permission for older Android versions
          status = await Permission.storage.status;
        }
      } else {
        // iOS uses photos permission
        status = await Permission.photos.status;
      }

      AppLogger.debug('Photo library permission status: $status');

      if (status.isGranted || status.isLimited) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Photo library access is required to select images. Please enable it in Settings.',
        );
      }

      // Request permission
      if (Platform.isAndroid) {
        status = await Permission.photos.request();
        if (status == PermissionStatus.denied) {
          // Fallback to storage permission for older Android versions
          status = await Permission.storage.request();
        }
      } else {
        status = await Permission.photos.request();
      }

      AppLogger.debug('Photo library permission after request: $status');

      if (status.isGranted || status.isLimited) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Photo library access is required to select images. Please enable it in Settings.',
        );
      }

      return PermissionResult.denied(
        'Photo library access is required to select images.',
      );
    } catch (e) {
      AppLogger.error('Error requesting photo library permission', error: e);
      return PermissionResult.error(
        'Unable to check photo library permissions. Please try again.',
      );
    }
  }

  /// Check and request video library permission
  Future<PermissionResult> requestVideoLibraryPermission() async {
    try {
      PermissionStatus status;

      // Use appropriate permission based on platform and Android version
      if (Platform.isAndroid) {
        // Try videos permission first (Android 13+)
        status = await Permission.videos.status;
        if (status == PermissionStatus.denied) {
          // Fallback to storage permission for older Android versions
          status = await Permission.storage.status;
        }
      } else {
        // iOS uses photos permission for videos too
        status = await Permission.photos.status;
      }

      AppLogger.debug('Video library permission status: $status');

      if (status.isGranted || status.isLimited) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Video library access is required to select videos. Please enable it in Settings.',
        );
      }

      // Request permission
      if (Platform.isAndroid) {
        status = await Permission.videos.request();
        if (status == PermissionStatus.denied) {
          // Fallback to storage permission for older Android versions
          status = await Permission.storage.request();
        }
      } else {
        status = await Permission.photos.request();
      }

      AppLogger.debug('Video library permission after request: $status');

      if (status.isGranted || status.isLimited) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Video library access is required to select videos. Please enable it in Settings.',
        );
      }

      return PermissionResult.denied(
        'Video library access is required to select videos.',
      );
    } catch (e) {
      AppLogger.error('Error requesting video library permission', error: e);
      return PermissionResult.error(
        'Unable to check video library permissions. Please try again.',
      );
    }
  }

  /// Check and request microphone permission
  Future<PermissionResult> requestMicrophonePermission() async {
    try {
      var status = await Permission.microphone.status;
      AppLogger.debug('Microphone permission status: $status');

      if (status.isGranted) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Microphone access is required to record audio with videos. Please enable it in Settings.',
        );
      }

      // Request permission
      status = await Permission.microphone.request();
      AppLogger.debug('Microphone permission after request: $status');

      if (status.isGranted) {
        return PermissionResult.granted();
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult.permanentlyDenied(
          'Microphone access is required to record audio with videos. Please enable it in Settings.',
        );
      }

      return PermissionResult.denied(
        'Microphone access is required to record audio with videos.',
      );
    } catch (e) {
      AppLogger.error('Error requesting microphone permission', error: e);
      return PermissionResult.error(
        'Unable to check microphone permissions. Please try again.',
      );
    }
  }

  /// Check and request both camera and microphone permissions for video recording
  Future<PermissionResult> requestVideoRecordingPermissions() async {
    try {
      final cameraResult = await requestCameraPermission();
      if (!cameraResult.isGranted) {
        return cameraResult;
      }

      final microphoneResult = await requestMicrophonePermission();
      if (!microphoneResult.isGranted) {
        return microphoneResult;
      }

      return PermissionResult.granted();
    } catch (e) {
      AppLogger.error('Error requesting video recording permissions', error: e);
      return PermissionResult.error(
        'Unable to check video recording permissions. Please try again.',
      );
    }
  }

  /// Test camera access by attempting to use it (fallback for iOS debug issues)
  Future<bool> testCameraAccess() async {
    try {
      AppLogger.debug('Testing camera access by attempting to use ImagePicker');
      final picker = ImagePicker();

      // Try to access camera with a very short timeout
      await picker
          .pickImage(
            source: ImageSource.camera,
            imageQuality: 1, // Lowest quality for test
          )
          .timeout(const Duration(seconds: 2), onTimeout: () => null);

      // If we got here without an exception, camera access works
      AppLogger.debug('Camera access test successful');
      return true;
    } catch (e) {
      AppLogger.debug('Camera access test failed: $e');
      return false;
    }
  }

  /// Test photo library access by attempting to use it (fallback for iOS debug issues)
  Future<bool> testPhotoLibraryAccess() async {
    try {
      AppLogger.debug(
        'Testing photo library access by attempting to use ImagePicker',
      );
      final picker = ImagePicker();

      // Try to access gallery with a very short timeout
      await picker
          .pickImage(
            source: ImageSource.gallery,
            imageQuality: 1, // Lowest quality for test
          )
          .timeout(const Duration(seconds: 2), onTimeout: () => null);

      // If we got here without an exception, photo library access works
      AppLogger.debug('Photo library access test successful');
      return true;
    } catch (e) {
      AppLogger.debug('Photo library access test failed: $e');
      return false;
    }
  }

  /// Open app settings
  Future<bool> openSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      AppLogger.error('Error opening app settings', error: e);
      return false;
    }
  }
}

/// Result of a permission request
class PermissionResult {
  final PermissionStatus status;
  final String? message;

  PermissionResult._(this.status, this.message);

  factory PermissionResult.granted() =>
      PermissionResult._(PermissionStatus.granted, null);

  factory PermissionResult.denied(String message) =>
      PermissionResult._(PermissionStatus.denied, message);

  factory PermissionResult.permanentlyDenied(String message) =>
      PermissionResult._(PermissionStatus.permanentlyDenied, message);

  factory PermissionResult.error(String message) =>
      PermissionResult._(PermissionStatus.denied, message);

  bool get isGranted => status.isGranted || status.isLimited;
  bool get isDenied => status.isDenied;
  bool get isPermanentlyDenied => status.isPermanentlyDenied;
  bool get isError =>
      message != null && !isGranted && !isDenied && !isPermanentlyDenied;
}
