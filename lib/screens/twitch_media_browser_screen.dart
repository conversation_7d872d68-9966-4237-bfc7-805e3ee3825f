import 'package:flutter/material.dart';
import 'dart:io';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../services/twitch_media_service.dart';
import '../models/twitch_media_model.dart';
import '../widgets/common/gf_button.dart';
import 'post_composition_screen.dart';
import 'storage_management_screen.dart';
import 'video_editor_screen.dart';

class TwitchMediaBrowserScreen extends StatefulWidget {
  const TwitchMediaBrowserScreen({super.key});

  @override
  State<TwitchMediaBrowserScreen> createState() =>
      _TwitchMediaBrowserScreenState();
}

class _TwitchMediaBrowserScreenState extends State<TwitchMediaBrowserScreen>
    with WidgetsBindingObserver {
  bool _isLoading = true;
  bool _isTwitchLinked = false;
  String? _error;

  List<TwitchMediaItem> _clips = [];
  final List<TwitchMediaItem> _selectedItems = [];
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  int _totalItemsToDownload = 0;
  int _completedDownloads = 0;

  // Track download status for each media item
  final Map<String, bool> _downloadStatus = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    AppLogger.info('Twitch media browser screen - initializing');
    _checkTwitchAccountAndLoadMedia();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh download status when app resumes
      _refreshDownloadStatus();
    }
  }

  Future<void> _checkTwitchAccountAndLoadMedia() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // For now, assume Twitch is linked if we can fetch clips
      // In a real implementation, you'd check if Twitch account is linked
      await _loadTwitchMedia();

      setState(() {
        _isTwitchLinked = true;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load Twitch media: $e';
        _isLoading = false;
        _isTwitchLinked = false;
      });
      AppLogger.error('Error loading Twitch media: $e');
    }
  }

  Future<void> _loadTwitchMedia() async {
    try {
      // Load clips
      final clipsResponse = await TwitchMediaService.instance.getClips(
        first: 50,
      );

      if (clipsResponse != null) {
        setState(() {
          _clips = clipsResponse.items;
        });

        // Check download status for each clip
        for (final clip in _clips) {
          final isDownloaded = await TwitchMediaService.instance.isDownloaded(
            clip.id,
          );
          setState(() {
            _downloadStatus[clip.id] = isDownloaded;
          });
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load Twitch clips: $e';
        _isLoading = false;
      });
      AppLogger.error('Error loading Twitch clips: $e');
    }
  }

  void _toggleSelection(TwitchMediaItem item) {
    setState(() {
      if (_selectedItems.contains(item)) {
        _selectedItems.remove(item);
      } else {
        _selectedItems.add(item);
      }
    });
  }

  /// Handle download or edit button tap
  Future<void> _handleDownloadOrEdit(TwitchMediaItem clip) async {
    final isDownloaded = _downloadStatus[clip.id] ?? false;

    if (isDownloaded) {
      // Clip is downloaded, open video editor directly
      try {
        final file = await TwitchMediaService.instance.getLocalFile(clip.id);
        if (file != null && await file.exists() && mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => VideoEditorScreen(
                    videoFile: file,
                    onVideoEditingComplete: (editedVideoFile) async {
                      // Navigate to post composition after editing
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder:
                              (context) => PostCompositionScreen(
                                videoFile: editedVideoFile,
                              ),
                        ),
                      );
                    },
                  ),
            ),
          );
        } else {
          // File doesn't exist, update status
          setState(() {
            _downloadStatus[clip.id] = false;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Downloaded file not found. Please download again.',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      } catch (e) {
        AppLogger.error(
          'Error opening video editor for clip ${clip.id}',
          error: e,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error opening video editor'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      // Clip is not downloaded, download it
      await _downloadSingleClip(clip);
    }
  }

  /// Download a single clip
  Future<void> _downloadSingleClip(TwitchMediaItem clip) async {
    try {
      setState(() {
        _isDownloading = true;
      });

      await TwitchMediaService.instance.downloadClip(clip);

      // Verify the download actually succeeded by checking file existence
      final actuallyDownloaded = await TwitchMediaService.instance.isDownloaded(
        clip.id,
      );

      AppLogger.debug(
        'Download verification for ${clip.id}: actuallyDownloaded = $actuallyDownloaded',
      );

      setState(() {
        _downloadStatus[clip.id] = actuallyDownloaded;
        _isDownloading = false;
      });

      if (mounted) {
        if (actuallyDownloaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Downloaded "${clip.title}" successfully'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
          // Refresh all download statuses to ensure UI is up to date
          await _refreshDownloadStatus();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Download completed but file not found for "${clip.title}"',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Failed to download clip ${clip.id}: $e');

      setState(() {
        _isDownloading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download "${clip.title}"'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Refresh download status for all clips
  Future<void> _refreshDownloadStatus() async {
    AppLogger.debug('Refreshing download status for ${_clips.length} clips');
    for (final clip in _clips) {
      final isDownloaded = await TwitchMediaService.instance.isDownloaded(
        clip.id,
      );
      setState(() {
        _downloadStatus[clip.id] = isDownloaded;
      });
    }
    AppLogger.debug('Download status refresh complete');
  }

  /// Delete a downloaded Twitch clip
  Future<void> _deleteDownloadedClip(TwitchMediaItem clip) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.gfDarkBackground,
          title: const Text(
            'Delete Downloaded Clip',
            style: TextStyle(color: AppColors.gfOffWhite),
          ),
          content: Text(
            'Are you sure you want to delete "${clip.title}"? This action cannot be undone.',
            style: const TextStyle(color: AppColors.gfOffWhite),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppColors.gfOffWhite),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        final success = await TwitchMediaService.instance.deleteDownloadedClip(
          clip,
        );
        if (success && mounted) {
          setState(() {
            _downloadStatus[clip.id] = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Deleted clip successfully'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete clip'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        AppLogger.error('Error deleting Twitch clip', error: e);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error deleting clip'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _downloadSelectedItems() async {
    if (_selectedItems.isEmpty) return;

    setState(() {
      _isDownloading = true;
      _totalItemsToDownload = _selectedItems.length;
      _completedDownloads = 0;
      _downloadProgress = 0.0;
    });

    try {
      for (int i = 0; i < _selectedItems.length; i++) {
        final item = _selectedItems[i];

        try {
          await TwitchMediaService.instance.downloadClip(item);

          // Verify the download actually succeeded by checking file existence
          final actuallyDownloaded = await TwitchMediaService.instance
              .isDownloaded(item.id);

          setState(() {
            _downloadStatus[item.id] = actuallyDownloaded;
            _completedDownloads++;
            _downloadProgress = _completedDownloads / _totalItemsToDownload;
          });
        } catch (e) {
          AppLogger.error('Failed to download clip ${item.id}: $e');

          // Handle Twitch-specific download limitations
          if (e is TwitchDownloadException &&
              e.type == TwitchDownloadErrorType.invalidUrl) {
            // Show dialog explaining Twitch limitations and offer to open clip page
            if (mounted) {
              _showTwitchDownloadDialog(item);
            }
          }
          // Continue with other downloads
        }
      }

      // Navigate to post composition with downloaded clips
      if (mounted) {
        final downloadedFiles = <File>[];
        for (final item in _selectedItems) {
          final file = await TwitchMediaService.instance.getLocalFile(item.id);
          if (file != null) {
            downloadedFiles.add(file);
          }
        }

        if (downloadedFiles.isNotEmpty && mounted) {
          // For now, just take the first downloaded file
          // TODO: Support multiple files in post composition
          final firstFile = downloadedFiles.first;
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => VideoEditorScreen(
                    videoFile: firstFile,
                    onVideoEditingComplete: (editedVideoFile) async {
                      // Navigate to post composition after editing
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder:
                              (context) => PostCompositionScreen(
                                videoFile: editedVideoFile,
                              ),
                        ),
                      );
                    },
                  ),
            ),
          );
        }
      }
    } finally {
      setState(() {
        _isDownloading = false;
        _selectedItems.clear();
      });
    }
  }

  /// Show dialog explaining Twitch download limitations
  void _showTwitchDownloadDialog(TwitchMediaItem item) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.gfDarkBackground,
          title: Text(
            'Twitch Download Limitation',
            style: TextStyle(color: AppColors.gfOffWhite),
          ),
          content: Text(
            'Due to Twitch API limitations, clips cannot be downloaded directly. '
            'You can open the clip page in your browser to download it manually.',
            style: TextStyle(color: AppColors.gfOffWhite),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: AppColors.gfOffWhite),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Open the clip URL in browser
                // TODO: Add url_launcher dependency and implement
                // launch(item.downloadUrl);
              },
              child: Text(
                'Open in Browser',
                style: TextStyle(color: AppColors.gfGreen),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBackground,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Twitch Clips',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfOffWhite,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.storage),
            color: AppColors.gfOffWhite,
            onPressed: () async {
              await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const StorageManagementScreen(),
                ),
              );
              // Refresh download status when returning from storage management
              await _refreshDownloadStatus();
            },
            tooltip: 'Storage Management',
          ),
          ..._buildSelectionActions(),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// Build action buttons based on current selection
  List<Widget> _buildSelectionActions() {
    if (_selectedItems.isEmpty) return [];

    // Check how many selected items are downloaded vs not downloaded
    final downloadedCount =
        _selectedItems
            .where((item) => _downloadStatus[item.id] ?? false)
            .length;
    final notDownloadedCount = _selectedItems.length - downloadedCount;

    List<Widget> actions = [];

    // If there are non-downloaded items, show download button
    if (notDownloadedCount > 0) {
      actions.add(
        IconButton(
          icon: Icon(Icons.download, color: AppColors.gfGreen),
          onPressed: _isDownloading ? null : _downloadSelectedItems,
          tooltip: 'Download selected clips ($notDownloadedCount)',
        ),
      );
    }

    // If there are downloaded items, show edit and delete buttons
    if (downloadedCount > 0) {
      // Edit button (for single downloaded item)
      if (downloadedCount == 1 && _selectedItems.length == 1) {
        actions.add(
          IconButton(
            icon: Icon(Icons.edit, color: AppColors.gfBlue),
            onPressed: () => _editSelectedItem(),
            tooltip: 'Edit selected clip',
          ),
        );
      }

      // Delete button
      actions.add(
        IconButton(
          icon: Icon(Icons.delete, color: Colors.red),
          onPressed: () => _deleteSelectedItems(),
          tooltip: 'Delete selected clips ($downloadedCount)',
        ),
      );
    }

    return actions;
  }

  /// Edit the selected item (only works for single downloaded item)
  Future<void> _editSelectedItem() async {
    if (_selectedItems.length != 1) return;

    final clip = _selectedItems.first;
    final isDownloaded = _downloadStatus[clip.id] ?? false;

    if (!isDownloaded) return;

    try {
      final file = await TwitchMediaService.instance.getLocalFile(clip.id);
      if (file != null && await file.exists() && mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => VideoEditorScreen(
                  videoFile: file,
                  onVideoEditingComplete: (editedVideoFile) async {
                    // Navigate to post composition after editing
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder:
                            (context) => PostCompositionScreen(
                              videoFile: editedVideoFile,
                            ),
                      ),
                    );
                  },
                ),
          ),
        );
      }
    } catch (e) {
      AppLogger.error(
        'Error opening video editor for clip ${clip.id}',
        error: e,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error opening video editor'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Delete all selected downloaded items
  Future<void> _deleteSelectedItems() async {
    final downloadedItems =
        _selectedItems
            .where((item) => _downloadStatus[item.id] ?? false)
            .toList();

    if (downloadedItems.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.gfDarkBackground,
          title: const Text(
            'Delete Downloaded Clips',
            style: TextStyle(color: AppColors.gfOffWhite),
          ),
          content: Text(
            'Are you sure you want to delete ${downloadedItems.length} downloaded clip(s)? This action cannot be undone.',
            style: const TextStyle(color: AppColors.gfOffWhite),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppColors.gfOffWhite),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );

    if (confirmed == true && mounted) {
      int deletedCount = 0;

      for (final item in downloadedItems) {
        try {
          final success = await TwitchMediaService.instance
              .deleteDownloadedClip(item);
          if (success) {
            setState(() {
              _downloadStatus[item.id] = false;
            });
            deletedCount++;
          }
        } catch (e) {
          AppLogger.error('Failed to delete clip ${item.id}: $e');
        }
      }

      // Clear selection after deletion
      setState(() {
        _selectedItems.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deleted $deletedCount clip(s) successfully'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    }
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Error',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16, color: AppColors.gfGrayText),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            GFButton(text: 'Retry', onPressed: _checkTwitchAccountAndLoadMedia),
          ],
        ),
      );
    }

    if (!_isTwitchLinked) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.link_off, size: 64, color: AppColors.gfGrayText),
            const SizedBox(height: 16),
            const Text(
              'No Twitch Account Linked',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Link your Twitch account to access your clips',
              style: TextStyle(fontSize: 16, color: AppColors.gfGrayText),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            GFButton(
              text: 'Link Twitch Account',
              onPressed: () {
                // TODO: Navigate to Twitch link screen
                // Navigator.of(context).push(
                //   MaterialPageRoute(
                //     builder: (context) => const TwitchLinkScreen(),
                //   ),
                // );
              },
            ),
          ],
        ),
      );
    }

    if (_clips.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              size: 64,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Clips Found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'You don\'t have any Twitch clips yet',
              style: TextStyle(fontSize: 16, color: AppColors.gfGrayText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        if (_isDownloading) _buildDownloadProgress(),
        Expanded(child: _buildClipsGrid()),
      ],
    );
  }

  Widget _buildDownloadProgress() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.gfCardBackground,
      child: Column(
        children: [
          Text(
            'Downloading $_completedDownloads of $_totalItemsToDownload clips...',
            style: const TextStyle(fontSize: 16, color: AppColors.gfOffWhite),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _downloadProgress,
            backgroundColor: AppColors.gfDarkBackground,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
          ),
        ],
      ),
    );
  }

  Widget _buildClipsGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 16 / 9,
      ),
      itemCount: _clips.length,
      itemBuilder: (context, index) {
        final clip = _clips[index];
        final isSelected = _selectedItems.contains(clip);
        final isDownloaded = _downloadStatus[clip.id] ?? false;

        // Debug logging
        AppLogger.debug(
          'Clip ${clip.id}: isDownloaded = $isDownloaded, status in map = ${_downloadStatus[clip.id]}',
        );

        return GestureDetector(
          onTap: () => _toggleSelection(clip),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.gfGreen : Colors.transparent,
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Thumbnail
                  Image.network(
                    clip.thumbnailUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: AppColors.gfCardBackground,
                        child: Icon(
                          Icons.video_library,
                          color: AppColors.gfGrayText,
                          size: 32,
                        ),
                      );
                    },
                  ),
                  // Overlay with clip info
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [
                            Colors.black.withValues(alpha: 0.8),
                            Colors.transparent,
                          ],
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            clip.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                clip.formattedDuration,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 10,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                clip.formattedViewCount,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Selection indicator
                  if (isSelected)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: AppColors.gfGreen,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  // Download status indicator (top left)
                  if (isDownloaded)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: AppColors.gfGreen,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.download_done,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),

                  // Download/Edit button (top right)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () => _handleDownloadOrEdit(clip),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color:
                              isDownloaded
                                  ? AppColors.gfGreen
                                  : AppColors.gfBlue,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isDownloaded ? Icons.check : Icons.download,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                  // Delete button for downloaded clips
                  if (isDownloaded)
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () => _deleteDownloadedClip(clip),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
