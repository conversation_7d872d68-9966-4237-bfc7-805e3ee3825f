import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/auth_provider.dart';
import '../providers/user_profile_provider.dart';
import '../theme/app_theme.dart';
import '../components/index.dart';
import '../utils/username_validator.dart';
import '../services/aws_user_service.dart';

class ChangeUsernameScreen extends StatefulWidget {
  const ChangeUsernameScreen({super.key});

  @override
  State<ChangeUsernameScreen> createState() => _ChangeUsernameScreenState();
}

class _ChangeUsernameScreenState extends State<ChangeUsernameScreen> {
  final _usernameController = TextEditingController();
  final _focusNode = FocusNode();

  bool _isLoading = false;
  bool _isCheckingAvailability = false;
  UsernameValidationResult? _validationResult;
  Timer? _debounceTimer;
  String? _currentUsername;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _usernameController.addListener(_onUsernameChanged);
    _loadCurrentUsername();
  }

  @override
  void dispose() {
    _usernameController.removeListener(_onUsernameChanged);
    _usernameController.dispose();
    _focusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _loadCurrentUsername() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    _currentUsername = authProvider.user?.username;
  }

  void _onUsernameChanged() {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Clear any previous error messages
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
      });
    }

    // Reset validation result for immediate feedback
    final localValidation = UsernameValidator.validateLocally(
      _usernameController.text,
    );
    setState(() {
      _validationResult = localValidation;
      _isCheckingAvailability = false;
    });

    // If local validation passes, check availability after a delay
    if (localValidation.isValid && _usernameController.text.trim().isNotEmpty) {
      _debounceTimer = Timer(const Duration(milliseconds: 800), () {
        _checkUsernameAvailability();
      });
    }
  }

  Future<void> _checkUsernameAvailability() async {
    if (!mounted) return;

    setState(() {
      _isCheckingAvailability = true;
    });

    try {
      final result = await UsernameValidator.checkAvailability(
        _usernameController.text,
      );
      if (mounted) {
        setState(() {
          _validationResult = result;
          _isCheckingAvailability = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _validationResult = const UsernameValidationResult(
            isValid: false,
            isAvailable: false,
            error: 'Connection error',
            details: 'Unable to check username availability',
          );
          _isCheckingAvailability = false;
        });
      }
    }
  }

  Future<void> _changeUsername() async {
    // Check if we have a valid and available username
    if (_validationResult == null || !_validationResult!.isValidAndAvailable) {
      // Force a validation check
      await _checkUsernameAvailability();
      if (_validationResult == null ||
          !_validationResult!.isValidAndAvailable) {
        return;
      }
    }

    // Check if the new username is the same as current username
    if (_usernameController.text.trim().toLowerCase() ==
        _currentUsername?.toLowerCase()) {
      setState(() {
        _errorMessage =
            'The new username is the same as your current username.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final success = await AwsUserService.instance.changeUsername(
        username: _usernameController.text.trim(),
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          // Refresh user profile to get updated username
          final userProfileProvider = Provider.of<UserProfileProvider>(
            context,
            listen: false,
          );
          await userProfileProvider.refreshProfile();

          // Show success message and go back
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Username changed successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop();
          }
        } else {
          setState(() {
            _errorMessage = 'Failed to change username. Please try again.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage =
              e.toString().contains('cooldown')
                  ? 'You can only change your username once per month.'
                  : e.toString().contains('limit exceeded')
                  ? 'You have reached the maximum of 5 username changes per year.'
                  : 'Failed to change username: ${e.toString()}';
        });
      }
    }
  }

  Widget _buildValidationIndicator() {
    if (_usernameController.text.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    if (_isCheckingAvailability) {
      return Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Checking availability...',
            style: TextStyle(color: AppColors.gfGreen, fontSize: 14),
          ),
        ],
      );
    }

    if (_validationResult != null) {
      if (_validationResult!.isValidAndAvailable) {
        return Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.gfGreen, size: 16),
            const SizedBox(width: 8),
            Text(
              UsernameValidator.getSuccessMessage(_validationResult!),
              style: TextStyle(color: AppColors.gfGreen, fontSize: 14),
            ),
          ],
        );
      } else {
        return Row(
          children: [
            const Icon(Icons.error, color: Colors.red, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                UsernameValidator.getErrorMessage(_validationResult!),
                style: const TextStyle(color: Colors.red, fontSize: 14),
              ),
            ),
          ],
        );
      }
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground100,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBackground100,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Change Username',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current username display
            if (_currentUsername != null) ...[
              Text(
                'Current Username',
                style: TextStyle(
                  color: AppColors.gfOffWhite.withValues(alpha: 0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.gfCardBackground,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.gfGreen.withAlpha(77),
                    width: 1,
                  ),
                ),
                child: Text(
                  _currentUsername!,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],

            // New username input
            GFTextField(
              label: 'New Username',
              hint: 'Enter your new username',
              controller: _usernameController,
              enabled: !_isLoading,
            ),
            const SizedBox(height: 12),

            // Validation indicator
            _buildValidationIndicator(),
            const SizedBox(height: 24),

            // Error message
            if (_errorMessage != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.red.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red, fontSize: 14),
                ),
              ),
              const SizedBox(height: 24),
            ],

            // Limitations info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.gfCardBackground,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.gfGreen.withAlpha(77),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Username Change Limitations',
                    style: TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '• You can only change your username once per month\n'
                    '• Maximum of 5 username changes per year\n'
                    '• Username must be 4-32 characters long\n'
                    '• Only letters, numbers, underscores, and hyphens allowed',
                    style: TextStyle(
                      color: AppColors.gfOffWhite.withValues(alpha: 0.8),
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            const Spacer(),

            // Change username button
            SizedBox(
              width: double.infinity,
              child: GFButton(
                text: _isLoading ? 'Changing Username...' : 'Change Username',
                onPressed:
                    _isLoading ||
                            _validationResult == null ||
                            !_validationResult!.isValidAndAvailable
                        ? null
                        : _changeUsername,
                isLoading: _isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
