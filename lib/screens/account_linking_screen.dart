import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';

import '../services/xbox_auth_service.dart';
import '../services/twitch_auth_service.dart';
import '../services/kick_auth_service.dart';
import '../services/apple_signin_service.dart';
import '../services/background_profile_cache_service.dart';
import '../providers/auth_provider.dart';
import '../utils/app_logger.dart';
import 'xbox_link_screen.dart';

class AccountLinkingScreen extends StatefulWidget {
  const AccountLinkingScreen({super.key});

  @override
  State<AccountLinkingScreen> createState() => _AccountLinkingScreenState();
}

class _AccountLinkingScreenState extends State<AccountLinkingScreen> {
  bool _isLoading = false;

  // Xbox linking state
  bool _isXboxLinked = false;
  String? _linkedXboxGamertag;

  // Twitch linking state
  bool _isTwitchLinked = false;
  // String? _linkedTwitchUsername; // Unused field
  TwitchAccount? _twitchAccount;

  // Kick linking state
  bool _isKickLinked = false;
  String? _linkedKickUsername;

  // Apple linking state
  bool _isAppleLinked = false;
  String? _linkedAppleEmail;

  @override
  void initState() {
    super.initState();
    _checkLinkedAccounts();
  }

  Future<void> _checkLinkedAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check Xbox account status with force refresh
      final isXboxLinked = await XboxAuthService.instance.isXboxAccountLinked(
        forceRefresh: true,
      );
      final xboxGamertag = XboxAuthService.instance.linkedGamertag;

      // Check Twitch account status and get full details
      final isTwitchLinked = await TwitchAuthService.instance
          .isTwitchAccountLinked(forceRefresh: true);
      // final twitchUsername = TwitchAuthService.instance.linkedUsername; // Unused variable
      final twitchAccount = await TwitchAuthService.instance.getTwitchAccount();

      // TODO: Check other services when their status checking methods are implemented
      // For now, we'll set them to false until the backend provides status endpoints

      if (mounted) {
        setState(() {
          _isXboxLinked = isXboxLinked;
          _linkedXboxGamertag = xboxGamertag;

          _isTwitchLinked = isTwitchLinked;
          // _linkedTwitchUsername = twitchUsername; // Unused field
          _twitchAccount = twitchAccount;

          // Set other services to false for now
          _isKickLinked = false;
          _linkedKickUsername = null;
          _isAppleLinked = false;
          _linkedAppleEmail = null;
        });
      }
    } catch (e) {
      AppLogger.error('Error checking linked accounts: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _navigateToXboxLinking() async {
    final result = await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const XboxLinkScreen()));

    // Refresh account status after returning
    if (result != null || mounted) {
      _checkLinkedAccounts();
    }
  }

  Future<void> _linkTwitchAccount() async {
    // Check if account is already linked but missing scopes
    if (_isTwitchLinked &&
        _twitchAccount != null &&
        !_twitchAccount!.hasAllRequiredScopes) {
      // Show re-authorization dialog
      final shouldReauth = await _showTwitchReauthDialog();
      if (!shouldReauth) return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await TwitchAuthService.instance.startTwitchOAuth();
      if (success && mounted) {
        // Trigger background profile refresh after account linking
        BackgroundProfileCacheService.instance.refreshAfterProfileAction();

        final message =
            _isTwitchLinked
                ? 'Twitch account re-authorized successfully!'
                : 'Twitch account linked successfully!';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: AppColors.gfGreen),
        );
        // Invalidate cache and refresh account status
        TwitchAuthService.instance.invalidateCache();
        _checkLinkedAccounts();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to link Twitch account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show dialog asking user to re-authorize Twitch with new scopes
  Future<bool> _showTwitchReauthDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: AppColors.gfDarkBackground,
              title: Text(
                'Re-authorize Twitch Account',
                style: TextStyle(color: AppColors.gfOffWhite),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your Twitch account needs additional permissions to access clips.',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  const SizedBox(height: 16),
                  if (_twitchAccount?.missingScopes.isNotEmpty == true) ...[
                    Text(
                      'Missing permissions:',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...(_twitchAccount!.missingScopes.map(
                      (scope) => Padding(
                        padding: const EdgeInsets.only(left: 16, bottom: 4),
                        child: Text(
                          '• $scope',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    )),
                    const SizedBox(height: 16),
                  ],
                  Text(
                    'Would you like to re-authorize your account with the required permissions?',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    'Re-authorize',
                    style: TextStyle(color: AppColors.gfGreen),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  Future<void> _linkKickAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current user ID from auth provider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await KickAuthService.instance.linkKickAccount(userId);

      if (response.success && mounted) {
        // Trigger background profile refresh after account linking
        BackgroundProfileCacheService.instance.refreshAfterProfileAction();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Kick account linked successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
        _checkLinkedAccounts();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to link Kick account: ${response.message}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to link Kick account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _linkAppleAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Apple Sign In automatically handles linking to existing accounts
      final response = await AppleSignInService.instance.signIn();

      if (response.success && mounted) {
        // Trigger background profile refresh after account linking
        BackgroundProfileCacheService.instance.refreshAfterProfileAction();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Apple account linked successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
        _checkLinkedAccounts();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to link Apple account: ${response.message}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to link Apple account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBackground,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Link Accounts',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(
                  child: CircularProgressIndicator(color: AppColors.gfGreen),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      const Text(
                        'Connect Your Gaming Accounts',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'Link your gaming accounts to easily share screenshots, clips, and achievements with the GameFlex community.',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.gfGrayText,
                          height: 1.4,
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Gaming Platforms Section
                      const Text(
                        'Gaming Platforms',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Xbox Account Card
                      _buildAccountCard(
                        title: 'Xbox Live',
                        description:
                            'Access your Xbox screenshots and game clips',
                        icon: _buildXboxIcon(),
                        isLinked: _isXboxLinked,
                        linkedInfo:
                            _linkedXboxGamertag != null
                                ? 'Gamertag: $_linkedXboxGamertag'
                                : null,
                        onTap: _navigateToXboxLinking,
                      ),

                      const SizedBox(height: 16),

                      // Apple Account Card
                      _buildAccountCard(
                        title: 'Apple ID',
                        description:
                            'Sign in with your Apple ID for secure authentication',
                        icon: _buildAppleIcon(),
                        isLinked: _isAppleLinked,
                        linkedInfo:
                            _linkedAppleEmail != null
                                ? 'Email: $_linkedAppleEmail'
                                : null,
                        onTap: _linkAppleAccount,
                      ),

                      const SizedBox(height: 32),

                      // Social Platforms Section
                      const Text(
                        'Social Platforms',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Twitch Account Card
                      _buildTwitchAccountCard(),

                      const SizedBox(height: 16),

                      // Kick Account Card
                      _buildAccountCard(
                        title: 'Kick',
                        description: 'Connect your Kick streaming account',
                        icon: _buildKickIcon(),
                        isLinked: _isKickLinked,
                        linkedInfo:
                            _linkedKickUsername != null
                                ? 'Username: $_linkedKickUsername'
                                : null,
                        onTap: _linkKickAccount,
                      ),

                      const SizedBox(height: 32),

                      // Help text
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.gfCardBackground,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.gfGrayBorder),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.info_outline,
                                  color: AppColors.gfGreen,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Privacy & Security',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.gfOffWhite,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Your linked accounts are secure and only used to access your public gaming content. You can unlink any account at any time.',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.gfGrayText,
                                height: 1.4,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildAccountCard({
    required String title,
    required String description,
    required Widget icon,
    required bool isLinked,
    String? linkedInfo,
    String? statusInfo,
    Color? statusColor,
    String? buttonText,
    bool isComingSoon = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: isComingSoon ? onTap : onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isLinked ? AppColors.gfGreen : AppColors.gfGrayBorder,
            width: isLinked ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Platform icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.gfDarkBackground,
                borderRadius: BorderRadius.circular(8),
              ),
              child: icon,
            ),
            const SizedBox(width: 16),

            // Platform info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      if (isComingSoon) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.gfGrayText.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'Coming Soon',
                            style: TextStyle(
                              fontSize: 10,
                              color: AppColors.gfGrayText,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.gfGrayText,
                    ),
                  ),
                  if (isLinked && linkedInfo != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      linkedInfo,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.gfGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                  if (statusInfo != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      statusInfo,
                      style: TextStyle(
                        fontSize: 12,
                        color: statusColor ?? AppColors.gfGrayText,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Status indicator
            if (isLinked)
              const Icon(Icons.check_circle, color: AppColors.gfGreen, size: 24)
            else if (isComingSoon)
              const Icon(Icons.schedule, color: AppColors.gfGrayText, size: 24)
            else
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.gfGrayText,
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildXboxIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF107C10), // Xbox green
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: SvgPicture.asset(
          'assets/images/icons/third_party_auth/xbox.svg',
          width: 28,
          height: 28,
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        ),
      ),
    );
  }

  Widget _buildAppleIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(Icons.apple, color: Colors.white, size: 28),
      ),
    );
  }

  Widget _buildKickIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF53FC18), // Kick green
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: SvgPicture.asset(
          'assets/images/icons/third_party_auth/kick.svg',
          width: 28,
          height: 28,
          colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
        ),
      ),
    );
  }

  Widget _buildTwitchAccountCard() {
    String? linkedInfo;
    String? statusInfo;
    Color? statusColor;
    String buttonText = 'Link Account';

    if (_isTwitchLinked && _twitchAccount != null) {
      linkedInfo = 'Username: ${_twitchAccount!.username}';

      if (_twitchAccount!.hasAllRequiredScopes) {
        statusInfo = 'All permissions granted';
        statusColor = AppColors.gfGreen;
        buttonText = 'Re-link Account';
      } else {
        statusInfo = 'Missing permissions for clips';
        statusColor = Colors.orange;
        buttonText = 'Re-authorize';
      }
    }

    return _buildAccountCard(
      title: 'Twitch',
      description: 'Connect your Twitch channel and streams',
      icon: _buildTwitchIcon(),
      isLinked: _isTwitchLinked,
      linkedInfo: linkedInfo,
      statusInfo: statusInfo,
      statusColor: statusColor,
      buttonText: buttonText,
      onTap: _linkTwitchAccount,
    );
  }

  Widget _buildTwitchIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF9146FF), // Twitch purple
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: SvgPicture.asset(
          'assets/images/icons/third_party_auth/twitch.svg',
          width: 28,
          height: 28,
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        ),
      ),
    );
  }
}
