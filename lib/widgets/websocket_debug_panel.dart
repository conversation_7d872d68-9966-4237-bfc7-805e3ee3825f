import 'dart:convert';
import 'package:flutter/material.dart';
import '../services/websocket_service.dart' as ws;
import '../services/config_service.dart';
import '../services/aws_auth_service.dart';
import '../services/api_service.dart';
import '../utils/websocket_debug.dart';

/// Debug panel for WebSocket testing
class WebSocketDebugPanel extends StatefulWidget {
  const WebSocketDebugPanel({super.key});

  @override
  State<WebSocketDebugPanel> createState() => _WebSocketDebugPanelState();
}

class _WebSocketDebugPanelState extends State<WebSocketDebugPanel> {
  String _debugOutput = '';
  bool _isLoading = false;
  String _selectedApiCall = 'notifications';

  @override
  void initState() {
    super.initState();
    _loadInitialInfo();

    // Listen to connection state changes and refresh info
    ws.WebSocketService.instance.connectionStateStream.listen((state) {
      if (mounted) {
        _loadInitialInfo();
      }
    });

    // Listen to incoming WebSocket messages for debugging
    ws.WebSocketService.instance.messageStream.listen((message) {
      if (mounted) {
        setState(() {
          _debugOutput += '\n\n🔔 INCOMING WEBSOCKET MESSAGE:\n';
          _debugOutput += 'Type: ${message.type.name}\n';
          _debugOutput += 'Timestamp: ${message.timestamp}\n';
          _debugOutput += 'Data: ${message.data}\n';
        });
      }
    });

    // Listen specifically to notifications
    ws.WebSocketService.instance.notificationStream.listen((notification) {
      if (mounted) {
        setState(() {
          _debugOutput += '\n\n🔔 NOTIFICATION RECEIVED:\n';
          _debugOutput += 'ID: ${notification.id}\n';
          _debugOutput += 'Type: ${notification.type}\n';
          _debugOutput += 'Title: ${notification.title}\n';
          _debugOutput += 'Body: ${notification.body}\n';
          _debugOutput += 'From: ${notification.actorUsername}\n';
        });
      }
    });
  }

  Future<void> _loadInitialInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiUrl = await ConfigService.instance.getServerUrl();
      final wsUrl = await ConfigService.instance.getWebSocketUrl();
      final isAuth = AwsAuthService.instance.isAuthenticated;
      final wsService = ws.WebSocketService.instance;

      // Test WebSocket URL and get token info
      final urlTest = await wsService.testWebSocketUrl();

      String tokenInfo = 'No token';
      if (urlTest['tokenInfo'] != null) {
        final info = urlTest['tokenInfo'] as Map<String, dynamic>;
        tokenInfo = '''
Token User: ${info['sub'] ?? 'N/A'}
Token Use: ${info['token_use'] ?? 'N/A'}
Username: ${info['username'] ?? 'N/A'}
Email: ${info['email'] ?? 'N/A'}
Expires: ${info['exp'] != null ? DateTime.fromMillisecondsSinceEpoch(info['exp'] * 1000) : 'N/A'}
Is Expired: ${info['isExpired'] ?? 'N/A'}
''';
      }

      // Get debug info for detailed connection status
      final debugInfo = wsService.getDebugInfo();

      setState(() {
        _debugOutput = '''
Environment: ${ConfigService.instance.isDevelopment
            ? 'Development'
            : ConfigService.instance.isStaging
            ? 'Staging'
            : 'Production'}
API URL: $apiUrl
WebSocket URL: $wsUrl
Authenticated: $isAuth

WebSocket Connection:
State: ${wsService.connectionState.name}
Connected: ${wsService.isConnected}
Healthy: ${wsService.isHealthy}
Fully Subscribed: ${debugInfo['isFullySubscribed']}
Status: ${wsService.statusMessage}
Quality: ${wsService.connectionQuality}%
Reconnect Attempts: ${debugInfo['reconnectAttempts']}
Consecutive Failures: ${debugInfo['consecutiveFailures']}

User Information:
Current User ID: ${AwsAuthService.instance.currentUser?.id ?? 'Not available'}
Authenticated: ${AwsAuthService.instance.isAuthenticated}

Subscription Status:
Notifications: ${debugInfo['subscriptionStatus']['isSubscribedToNotifications']}
Device Registered: ${debugInfo['subscriptionStatus']['isDeviceRegistered']}
Device ID: ${debugInfo['subscriptionStatus']['registeredDeviceId'] ?? 'None'}

Stream Controllers:
Notifications: ${debugInfo['streamControllers']['notifications']}
Post Updates: ${debugInfo['streamControllers']['postUpdates']}
Comment Updates: ${debugInfo['streamControllers']['commentUpdates']}
Errors: ${debugInfo['streamControllers']['errors']}

Message Handlers:
${debugInfo['registeredHandlers'].entries.map((e) => '${e.key}: ${e.value} handlers').join('\n')}

JWT Token Info:
$tokenInfo

URL Test Result:
Success: ${urlTest['success']}
Has Token: ${urlTest['hasToken']}
Token Length: ${urlTest['tokenLength']}
${urlTest['error'] != null ? 'Error: ${urlTest['error']}' : ''}

Recent Errors: ${debugInfo['recentErrorCount']}
Last Ping: ${debugInfo['lastSuccessfulPing'] ?? 'Never'}
Last Message: ${debugInfo['lastMessageReceived'] ?? 'Never'}
''';
      });
    } catch (e) {
      setState(() {
        _debugOutput = 'Error loading info: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _debugOutput = 'Testing connection...\n';
    });

    try {
      await WebSocketDebug.testConnection();
      await _loadInitialInfo();
    } catch (e) {
      setState(() {
        _debugOutput += '\nTest failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testApiCall() async {
    setState(() {
      _isLoading = true;
      _debugOutput = 'Testing API call: $_selectedApiCall...\n';
    });

    try {
      final apiService = ApiService.instance;
      final response = await _makeApiCall(apiService, _selectedApiCall);

      setState(() {
        _debugOutput += 'API Call Result ($_selectedApiCall):\n';
        _debugOutput += 'Status: ${response.statusCode}\n';
        _debugOutput += 'Success: ${response.isSuccess}\n';

        if (response.isSuccess) {
          _debugOutput += 'Response Data:\n';
          if (response.data != null) {
            // Pretty print the response data
            final prettyData = _formatJsonResponse(response.data);
            _debugOutput += '$prettyData\n';
          } else {
            _debugOutput += 'No data returned\n';
          }
        } else {
          _debugOutput += 'Error: ${response.error}\n';
          _debugOutput += 'Message: ${response.message}\n';
        }

        _debugOutput += '\n--- End API Response ---\n';
      });
    } catch (e) {
      setState(() {
        _debugOutput += 'API test failed: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<dynamic> _makeApiCall(dynamic apiService, String endpoint) async {
    switch (endpoint) {
      case 'notifications':
        return await apiService.get('/notifications?limit=10');
      case 'health':
        return await apiService.get('/health');
      case 'channels':
        return await apiService.get('/channels?limit=5');
      case 'posts':
        return await apiService.get('/posts?limit=5');
      default:
        return await apiService.get('/health');
    }
  }

  String _formatJsonResponse(dynamic data) {
    try {
      if (data is Map || data is List) {
        // Convert to JSON string with indentation
        const encoder = JsonEncoder.withIndent('  ');
        return encoder.convert(data);
      } else {
        return data.toString();
      }
    } catch (e) {
      return 'Error formatting response: $e';
    }
  }

  Future<void> _printDebugInfo() async {
    await WebSocketDebug.printDebugInfo();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Debug info printed to console')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'WebSocket Debug Panel',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // API Call Selection
            Row(
              children: [
                const Text('API Call: '),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: _selectedApiCall,
                  onChanged:
                      _isLoading
                          ? null
                          : (String? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _selectedApiCall = newValue;
                              });
                            }
                          },
                  items: const [
                    DropdownMenuItem(
                      value: 'notifications',
                      child: Text('Notifications'),
                    ),
                    DropdownMenuItem(
                      value: 'health',
                      child: Text('Health Check'),
                    ),
                    DropdownMenuItem(
                      value: 'channels',
                      child: Text('Channels'),
                    ),
                    DropdownMenuItem(value: 'posts', child: Text('Posts')),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Action buttons
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _loadInitialInfo,
                  child: const Text('Refresh Info'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testApiCall,
                  child: Text('Test $_selectedApiCall'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testConnection,
                  child: const Text('Test WebSocket'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _printDebugInfo,
                  child: const Text('Print to Console'),
                ),
                ElevatedButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () {
                            ws.WebSocketService.instance.retryConnection();
                            _loadInitialInfo();
                          },
                  child: const Text('Retry Connection'),
                ),
                ElevatedButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () async {
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );
                            await ws.WebSocketService.instance
                                .subscribeToNotifications();
                            if (mounted) {
                              scaffoldMessenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Subscribed to notifications'),
                                ),
                              );
                            }
                          },
                  child: const Text('Subscribe to Notifications'),
                ),
                ElevatedButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () async {
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );
                            await ws.WebSocketService.instance
                                .registerDeviceId();
                            if (mounted) {
                              scaffoldMessenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Registered device ID'),
                                ),
                              );
                            }
                          },
                  child: const Text('Register Device ID'),
                ),
                ElevatedButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () async {
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );
                            await ws.WebSocketService.instance
                                .ensureSubscriptionsActive();
                            if (mounted) {
                              scaffoldMessenger.showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Ensured all subscriptions are active',
                                  ),
                                ),
                              );
                            }
                            _loadInitialInfo(); // Refresh the display
                          },
                  child: const Text('Ensure Subscriptions'),
                ),
                ElevatedButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () async {
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );
                            setState(() {
                              _isLoading = true;
                            });

                            try {
                              final wsService = ws.WebSocketService.instance;

                              // Force connect if not connected
                              if (!wsService.isConnected) {
                                await wsService.connect();
                                await Future.delayed(
                                  const Duration(seconds: 2),
                                );
                              }

                              // Test ping
                              await wsService.sendPing();
                              await Future.delayed(const Duration(seconds: 1));

                              // Ensure subscriptions
                              await wsService.ensureSubscriptionsActive();

                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Connection test complete: ${wsService.isConnected ? "Connected" : "Failed"}',
                                    ),
                                    backgroundColor:
                                        wsService.isConnected
                                            ? Colors.green
                                            : Colors.red,
                                  ),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text('Connection test failed: $e'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            } finally {
                              setState(() {
                                _isLoading = false;
                              });
                              _loadInitialInfo();
                            }
                          },
                  child: const Text('Force Connect & Test'),
                ),
                ElevatedButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () async {
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );
                            setState(() {
                              _isLoading = true;
                            });

                            try {
                              // Send a test message to yourself
                              final currentUserId =
                                  AwsAuthService.instance.currentUser?.id;
                              if (currentUserId == null) {
                                throw Exception(
                                  'Not authenticated - no user ID available',
                                );
                              }

                              final response = await ApiService.instance
                                  .makeAuthenticatedRequest(
                                    method: 'POST',
                                    path: '/notifications/test',
                                    body: {
                                      'type': 'follow',
                                      'title': 'WebSocket Test',
                                      'message':
                                          'Testing WebSocket delivery to user: $currentUserId',
                                      'targetUserId':
                                          currentUserId, // Explicitly set target user
                                    },
                                  );

                              final data = ApiService.instance.parseResponse(
                                response,
                              );

                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Test notification sent to $currentUserId',
                                    ),
                                    backgroundColor: Colors.blue,
                                  ),
                                );
                              }

                              setState(() {
                                _debugOutput += '\n\nTest Notification Sent:\n';
                                _debugOutput += 'Target User: $currentUserId\n';
                                _debugOutput +=
                                    'Response: ${data['message']}\n';
                              });
                            } catch (e) {
                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Failed to send test notification: $e',
                                    ),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            } finally {
                              setState(() {
                                _isLoading = false;
                              });
                            }
                          },
                  child: const Text('Send Test to Self'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Debug output
            Container(
              width: double.infinity,
              height: 300,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[900], // Dark background
                border: Border.all(color: Colors.grey[600]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child:
                  _isLoading
                      ? const Center(
                        child: CircularProgressIndicator(color: Colors.white),
                      )
                      : SingleChildScrollView(
                        child: SelectableText(
                          _debugOutput,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                            color:
                                Colors.white, // White text on dark background
                          ),
                        ),
                      ),
            ),

            const SizedBox(height: 16),

            // Real-time connection status indicator
            StreamBuilder<ws.ConnectionState>(
              stream: ws.WebSocketService.instance.connectionStateStream,
              builder: (context, snapshot) {
                return _buildConnectionStatus();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus() {
    final wsService = ws.WebSocketService.instance;
    final state = wsService.connectionState;
    final color = _getStateColor(state);
    final isConnected = wsService.isConnected;
    // final isHealthy = wsService.isHealthy; // Unused variable
    final quality = wsService.connectionQuality;
    final subscriptionStatus = wsService.getSubscriptionStatus();
    final statusMessage = wsService.statusMessage;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(color: color, shape: BoxShape.circle),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Status: $statusMessage',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: 14,
                  ),
                ),
              ),
              if (isConnected)
                Icon(Icons.wifi, color: color, size: 20)
              else
                Icon(Icons.wifi_off, color: color, size: 20),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatusChip('State', state.name, color),
              const SizedBox(width: 8),
              _buildStatusChip(
                'Quality',
                '$quality%',
                quality >= 80
                    ? Colors.green
                    : quality >= 50
                    ? Colors.orange
                    : Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatusChip(
                'Notifications',
                subscriptionStatus['isSubscribedToNotifications']
                    ? 'Yes'
                    : 'No',
                subscriptionStatus['isSubscribedToNotifications']
                    ? Colors.green
                    : Colors.red,
              ),
              const SizedBox(width: 8),
              _buildStatusChip(
                'Device',
                subscriptionStatus['isDeviceRegistered']
                    ? 'Registered'
                    : 'Not Registered',
                subscriptionStatus['isDeviceRegistered']
                    ? Colors.green
                    : Colors.red,
              ),
            ],
          ),
          if (subscriptionStatus['registeredDeviceId'] != null) ...[
            const SizedBox(height: 4),
            Text(
              'Device ID: ${subscriptionStatus['registeredDeviceId']}',
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          fontSize: 11,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getStateColor(ws.ConnectionState state) {
    switch (state) {
      case ws.ConnectionState.connected:
        return Colors.green;
      case ws.ConnectionState.connecting:
      case ws.ConnectionState.reconnecting:
        return Colors.orange;
      case ws.ConnectionState.error:
        return Colors.red;
      case ws.ConnectionState.degraded:
        return Colors.yellow;
      case ws.ConnectionState.disconnected:
        return Colors.grey;
    }
  }
}
