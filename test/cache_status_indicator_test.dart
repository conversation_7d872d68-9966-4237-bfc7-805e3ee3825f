import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/widgets/feed_item.dart';
import 'package:gameflex_mobile/models/post_model.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/services/config_service.dart';

void main() {
  group('Cache Status Indicator Tests', () {
    late PostModel testPost;
    late PostsProvider postsProvider;

    setUp(() {
      testPost = PostModel(
        id: 'test_post_1',
        userId: 'test_user_1',
        username: 'testuser',
        displayName: 'Test User',
        content: 'Test post content',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        likeCount: 0,
        commentCount: 0,
        reflexCount: 0,
        isActive: true,
        status: 'published',
        isLikedByCurrentUser: false,
        mediaUrl: null,
        mediaType: null,
        channelId: null,
        channelName: null,
      );

      postsProvider = PostsProvider();
    });

    testWidgets('Cache status indicator shows in development mode', (
      WidgetTester tester,
    ) async {
      // Mock development mode
      expect(ConfigService.instance.isDevelopment, isTrue);

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<PostsProvider>.value(
            value: postsProvider,
            child: Scaffold(body: FeedItem(post: testPost, isVisible: true)),
          ),
        ),
      );

      // Note: Cache status is now determined by media loading, not parameter
      // This test would need to be updated to test actual media loading
    });

    testWidgets('Network status indicator shows correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<PostsProvider>.value(
            value: postsProvider,
            child: Scaffold(body: FeedItem(post: testPost, isVisible: true)),
          ),
        ),
      );

      // Note: Cache status is now determined by media loading, not parameter
      // This test would need to be updated to test actual media loading
    });

    testWidgets('Cache status uses provider value when no override', (
      WidgetTester tester,
    ) async {
      // Set provider cache status
      postsProvider.setLastLoadFromCache(true);

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<PostsProvider>.value(
            value: postsProvider,
            child: Scaffold(
              body: FeedItem(
                post: testPost,
                isVisible: true,
                // No isFromCache override - should use provider value
              ),
            ),
          ),
        ),
      );

      // Should show cache indicator from provider
      expect(find.text('CACHE'), findsOneWidget);
    });
  });
}

// Extension to help with testing
extension PostsProviderTest on PostsProvider {
  void setLastLoadFromCache(bool value) {
    // This would need to be implemented in the actual provider
    // For now, this is just a placeholder for the test structure
  }
}
