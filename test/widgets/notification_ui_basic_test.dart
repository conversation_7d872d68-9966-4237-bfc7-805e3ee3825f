import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/notification_item.dart';

void main() {
  group('Notification UI Basic Tests', () {
    RealtimeNotificationModel createMockNotification({
      String id = 'test-id',
      String title = 'Test Notification',
      String body = 'Test notification body',
      bool isRead = false,
      NotificationType type = NotificationType.comment,
      String actorUserId = 'actor-id',
      String? actorUsername = 'testuser',
      String? actorDisplayName = 'Test User',
      String? actorAvatarUrl,
      DateTime? createdAt,
      bool isRealtime = true,
    }) {
      return RealtimeNotificationModel(
        id: id,
        userId: 'user-id',
        type: type,
        actorUserId: actorUserId,
        actorUsername: actorUsername,
        actorDisplayName: actorDisplayName,
        actorAvatarUrl: actorAvatarUrl,
        title: title,
        body: body,
        isRead: isRead,
        createdAt:
            createdAt ?? DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt:
            createdAt ?? DateTime.now().subtract(const Duration(hours: 1)),
        source: NotificationSource.websocket,
        receivedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRealtime: isRealtime,
      );
    }

    Widget createTestWidget({required Widget child}) {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(body: child),
      );
    }

    group('NotificationItem Tests', () {
      testWidgets('creates notification item without errors', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          actorDisplayName: 'John Doe',
          body: 'This is a test notification',
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        // Just verify the widget renders without throwing
        expect(find.byType(NotificationItem), findsOneWidget);
      });

      testWidgets('creates comment notification without errors', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          type: NotificationType.comment,
          actorDisplayName: 'John',
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        expect(find.byType(NotificationItem), findsOneWidget);
      });

      testWidgets('creates follow notification without errors', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          type: NotificationType.follow,
          actorDisplayName: 'Jane',
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        expect(find.byType(NotificationItem), findsOneWidget);
      });

      testWidgets('creates reflex notification without errors', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          type: NotificationType.reflex,
          actorDisplayName: 'Bob',
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        expect(find.byType(NotificationItem), findsOneWidget);
      });

      testWidgets('displays icons for different notification types', (
        WidgetTester tester,
      ) async {
        // Test comment notification
        final commentNotification = createMockNotification(
          type: NotificationType.comment,
        );
        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(notification: commentNotification),
          ),
        );
        expect(find.byIcon(Icons.comment), findsOneWidget);

        // Test follow notification
        final followNotification = createMockNotification(
          type: NotificationType.follow,
        );
        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(notification: followNotification),
          ),
        );
        expect(find.byIcon(Icons.person_add), findsOneWidget);

        // Test reflex notification
        final reflexNotification = createMockNotification(
          type: NotificationType.reflex,
        );
        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(notification: reflexNotification),
          ),
        );
        expect(find.byIcon(Icons.flash_on), findsOneWidget);
      });

      testWidgets('has tap functionality', (WidgetTester tester) async {
        final notification = createMockNotification();
        bool wasTapped = false;

        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(
              notification: notification,
              onTap: () => wasTapped = true,
            ),
          ),
        );

        // Find the main notification container and tap it
        await tester.tap(find.byType(NotificationItem));
        await tester.pumpAndSettle();

        expect(wasTapped, isTrue);
      });

      testWidgets('has mark as read functionality for unread notifications', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(isRead: false);
        bool wasMarkedAsRead = false;

        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(
              notification: notification,
              onMarkAsRead: () => wasMarkedAsRead = true,
            ),
          ),
        );

        // Tap the notification
        await tester.tap(find.byType(NotificationItem));
        await tester.pumpAndSettle();

        expect(wasMarkedAsRead, isTrue);
      });

      testWidgets('does not mark read notifications as read again', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(isRead: true);
        bool wasMarkedAsRead = false;

        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(
              notification: notification,
              onMarkAsRead: () => wasMarkedAsRead = true,
            ),
          ),
        );

        // Tap the notification
        await tester.tap(find.byType(NotificationItem));
        await tester.pumpAndSettle();

        expect(wasMarkedAsRead, isFalse);
      });

      testWidgets('has popup menu functionality', (WidgetTester tester) async {
        final notification = createMockNotification(isRead: false);

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        // Verify popup menu button exists
        expect(find.byIcon(Icons.more_vert), findsOneWidget);
      });

      testWidgets('shows different menu for follow notifications', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          type: NotificationType.follow,
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        // Verify popup menu button exists
        expect(find.byIcon(Icons.more_vert), findsOneWidget);
      });

      testWidgets('handles read notification menu', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(isRead: true);

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        // Verify popup menu button exists
        expect(find.byIcon(Icons.more_vert), findsOneWidget);
      });

      testWidgets('displays divider when showDivider is true', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification();

        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(
              notification: notification,
              showDivider: true,
            ),
          ),
        );

        expect(find.byType(Divider), findsOneWidget);
      });

      testWidgets('does not display divider when showDivider is false', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification();

        await tester.pumpWidget(
          createTestWidget(
            child: NotificationItem(
              notification: notification,
              showDivider: false,
            ),
          ),
        );

        expect(find.byType(Divider), findsNothing);
      });

      testWidgets('handles long notification text', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          body:
              'This is a very long notification body that should be truncated with ellipsis when it exceeds the maximum number of lines allowed for display in the notification item widget',
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        // Just verify the widget renders without errors
        expect(find.byType(NotificationItem), findsOneWidget);
      });

      testWidgets('displays timestamp information', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        );

        await tester.pumpWidget(
          createTestWidget(child: NotificationItem(notification: notification)),
        );

        // Just verify the widget renders without errors
        expect(find.byType(NotificationItem), findsOneWidget);
      });
    });

    group('CompactNotificationItem Tests', () {
      testWidgets('creates compact notification without errors', (
        WidgetTester tester,
      ) async {
        final notification = createMockNotification(title: 'Compact Test');

        await tester.pumpWidget(
          createTestWidget(
            child: CompactNotificationItem(notification: notification),
          ),
        );

        expect(find.byType(CompactNotificationItem), findsOneWidget);
      });

      testWidgets('has tap functionality', (WidgetTester tester) async {
        final notification = createMockNotification();
        bool wasTapped = false;

        await tester.pumpWidget(
          createTestWidget(
            child: CompactNotificationItem(
              notification: notification,
              onTap: () => wasTapped = true,
            ),
          ),
        );

        await tester.tap(find.byType(CompactNotificationItem));
        await tester.pumpAndSettle();

        expect(wasTapped, isTrue);
      });
    });

    group('TypedNotificationItem Tests', () {
      testWidgets('renders different types correctly', (
        WidgetTester tester,
      ) async {
        for (final type in NotificationType.values) {
          final notification = createMockNotification(type: type);

          await tester.pumpWidget(
            createTestWidget(
              child: TypedNotificationItem(notification: notification),
            ),
          );

          // Should render as NotificationItem for all types
          expect(find.byType(NotificationItem), findsOneWidget);
        }
      });
    });
  });
}
