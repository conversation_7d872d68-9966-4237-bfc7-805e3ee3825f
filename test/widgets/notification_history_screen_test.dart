import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
// import 'package:mockito/annotations.dart'; // Unused import
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';

import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/notification_history_screen.dart';

// Use the same mock file as notification_menu_test
import 'notification_menu_test.mocks.dart';

void main() {
  group('NotificationHistoryScreen Widget Tests', () {
    late MockRealtimeNotificationProvider mockProvider;

    setUp(() {
      mockProvider = MockRealtimeNotificationProvider();
    });

    Widget createTestWidget({
      Function(RealtimeNotificationModel)? onNotificationTap,
      List<RealtimeNotificationModel>? notifications,
      int unreadCount = 0,
      bool isLoading = false,
      bool hasMoreNotifications = true,
    }) {
      // Setup mock provider
      when(
        mockProvider.notifications,
      ).thenReturn(notifications ?? <RealtimeNotificationModel>[]);
      when(mockProvider.unreadCount).thenReturn(unreadCount);
      when(mockProvider.hasUnreadNotifications).thenReturn(unreadCount > 0);
      when(mockProvider.isLoading).thenReturn(isLoading);
      when(mockProvider.hasMoreNotifications).thenReturn(hasMoreNotifications);
      when(mockProvider.error).thenReturn(null);
      when(mockProvider.hasError).thenReturn(false);

      // Setup method returns
      when(mockProvider.getNotificationsByType(any)).thenReturn([]);
      when(
        mockProvider.loadNotifications(refresh: anyNamed('refresh')),
      ).thenAnswer((_) async {});
      when(mockProvider.loadMoreNotifications()).thenAnswer((_) async {});
      when(mockProvider.refreshNotifications()).thenAnswer((_) async {});
      when(mockProvider.markAsRead(any)).thenAnswer((_) async {});
      when(mockProvider.markAllAsRead()).thenAnswer((_) async {});

      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: ChangeNotifierProvider<RealtimeNotificationProvider>(
          create: (_) => mockProvider,
          child: NotificationHistoryScreen(
            onNotificationTap: onNotificationTap,
          ),
        ),
      );
    }

    RealtimeNotificationModel createMockNotification({
      String id = 'test-id',
      String title = 'Test Notification',
      String body = 'Test notification body',
      bool isRead = false,
      NotificationType type = NotificationType.comment,
      String actorUserId = 'actor-id',
      String? actorUsername = 'testuser',
      String? actorDisplayName = 'Test User',
    }) {
      return RealtimeNotificationModel(
        id: id,
        userId: 'user-id',
        type: type,
        actorUserId: actorUserId,
        actorUsername: actorUsername,
        actorDisplayName: actorDisplayName,
        title: title,
        body: body,
        isRead: isRead,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        source: NotificationSource.websocket,
        receivedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRealtime: true,
      );
    }

    testWidgets('displays app bar with correct title', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Notifications'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets(
      'displays loading indicator when loading initial notifications',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(notifications: [], isLoading: true),
        );

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      },
    );

    testWidgets('displays empty state when no notifications', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(notifications: []));

      expect(find.text('No notifications yet'), findsOneWidget);
      expect(
        find.text('You\'ll see notifications here when you have new activity'),
        findsOneWidget,
      );
    });

    testWidgets('displays notification list when notifications exist', (
      WidgetTester tester,
    ) async {
      final notifications = [
        createMockNotification(id: '1', title: 'Notification 1'),
        createMockNotification(id: '2', title: 'Notification 2'),
      ];

      await tester.pumpWidget(createTestWidget(notifications: notifications));

      expect(find.text('Test notification body'), findsNWidgets(2));
    });

    testWidgets(
      'displays mark all read button when there are unread notifications',
      (WidgetTester tester) async {
        final notifications = [createMockNotification(isRead: false)];

        await tester.pumpWidget(
          createTestWidget(notifications: notifications, unreadCount: 1),
        );

        expect(find.text('Mark all read'), findsOneWidget);
      },
    );

    testWidgets(
      'does not display mark all read button when no unread notifications',
      (WidgetTester tester) async {
        final notifications = [createMockNotification(isRead: true)];

        await tester.pumpWidget(
          createTestWidget(notifications: notifications, unreadCount: 0),
        );

        expect(find.text('Mark all read'), findsNothing);
      },
    );

    testWidgets('calls markAllAsRead when mark all read is tapped', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification(isRead: false)];

      await tester.pumpWidget(
        createTestWidget(notifications: notifications, unreadCount: 1),
      );

      await tester.tap(find.text('Mark all read'));
      await tester.pumpAndSettle();

      verify(mockProvider.markAllAsRead()).called(1);
    });

    testWidgets('displays search bar', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Search notifications...'), findsOneWidget);
    });

    testWidgets('filters notifications by search query', (
      WidgetTester tester,
    ) async {
      final notifications = [
        createMockNotification(id: '1', title: 'Important notification'),
        createMockNotification(id: '2', title: 'Regular update'),
      ];

      await tester.pumpWidget(createTestWidget(notifications: notifications));

      // Enter search query
      await tester.enterText(find.byType(TextField), 'Important');
      await tester.pumpAndSettle();

      // Wait for debounce
      await tester.pump(const Duration(milliseconds: 600));

      // Should filter results (though in this test we can't verify the actual filtering
      // since it's done in the widget logic, but we can verify the search field works)
      expect(find.text('Important'), findsOneWidget);
    });

    testWidgets('displays filter chips', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(unreadCount: 5));

      expect(find.text('Unread (5)'), findsOneWidget);
      expect(find.text('New Followers (0)'), findsOneWidget);
      expect(find.text('Reflexes (0)'), findsOneWidget);
      expect(find.text('Comments (0)'), findsOneWidget);
    });

    testWidgets('toggles unread filter when chip is tapped', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(unreadCount: 3));

      final unreadChip = find.text('Unread (3)');
      expect(unreadChip, findsOneWidget);

      await tester.tap(unreadChip);
      await tester.pumpAndSettle();

      // Chip should be selected (visual change)
      final filterChip = tester.widget<FilterChip>(
        find.byWidgetPredicate(
          (widget) =>
              widget is FilterChip &&
              widget.label is Text &&
              (widget.label as Text).data!.contains('Unread'),
        ),
      );
      expect(filterChip.selected, isTrue);
    });

    testWidgets('displays pagination loader when has more notifications', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification()];

      await tester.pumpWidget(
        createTestWidget(
          notifications: notifications,
          hasMoreNotifications: true,
        ),
      );

      // Scroll to bottom
      await tester.drag(find.byType(ListView), const Offset(0, -500));
      await tester.pumpAndSettle();

      expect(find.text('Load more'), findsOneWidget);
    });

    testWidgets('calls loadMoreNotifications when scrolled to bottom', (
      WidgetTester tester,
    ) async {
      final notifications = List.generate(
        5,
        (i) => createMockNotification(id: 'id-$i'),
      );

      await tester.pumpWidget(
        createTestWidget(
          notifications: notifications,
          hasMoreNotifications: true,
        ),
      );

      // Scroll to bottom
      await tester.drag(find.byType(ListView), const Offset(0, -1000));
      await tester.pumpAndSettle();

      // Wait for debounce timer
      await tester.pump(const Duration(milliseconds: 300));

      verify(mockProvider.loadMoreNotifications()).called(1);
    });

    testWidgets('displays refresh indicator and calls refreshNotifications', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification()];

      await tester.pumpWidget(createTestWidget(notifications: notifications));

      // Pull to refresh
      await tester.drag(find.byType(RefreshIndicator), const Offset(0, 300));
      await tester.pumpAndSettle();

      verify(mockProvider.refreshNotifications()).called(1);
    });

    testWidgets('calls onNotificationTap when notification is tapped', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification();
      RealtimeNotificationModel? tappedNotification;

      await tester.pumpWidget(
        createTestWidget(
          notifications: [notification],
          onNotificationTap: (n) => tappedNotification = n,
        ),
      );

      await tester.tap(find.text('Test notification body'));
      await tester.pumpAndSettle();

      expect(tappedNotification, equals(notification));
    });

    testWidgets('displays popup menu with options', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      expect(find.text('Clear all'), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
    });

    testWidgets('shows clear all confirmation dialog', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Clear all'));
      await tester.pumpAndSettle();

      expect(find.text('Clear All Notifications'), findsOneWidget);
      expect(
        find.text(
          'This will permanently remove all notifications. This action cannot be undone.',
        ),
        findsOneWidget,
      );
      expect(find.text('Cancel'), findsOneWidget);
      expect(
        find.text('Clear All'),
        findsNWidgets(2),
      ); // One in menu, one in dialog
    });

    testWidgets('clears all notifications when confirmed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());

      // Open menu and select clear all
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Clear all'));
      await tester.pumpAndSettle();

      // Confirm in dialog
      await tester.tap(find.text('Clear All').last);
      await tester.pumpAndSettle();

      verify(mockProvider.clearAllNotifications()).called(1);
    });

    testWidgets(
      'displays empty state with clear filters button when filtered',
      (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget(notifications: []));

        // Enter search query to trigger filtered empty state
        await tester.enterText(find.byType(TextField), 'nonexistent');
        await tester.pumpAndSettle();

        // Wait for debounce
        await tester.pump(const Duration(milliseconds: 600));

        expect(find.text('Clear filters'), findsOneWidget);
      },
    );

    testWidgets('clears filters when clear filters button is tapped', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(notifications: []));

      // Enter search query
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pumpAndSettle();

      // Wait for debounce
      await tester.pump(const Duration(milliseconds: 600));

      // Tap clear filters
      await tester.tap(find.text('Clear filters'));
      await tester.pumpAndSettle();

      // Search field should be cleared
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, isEmpty);
    });

    testWidgets('handles error states gracefully', (WidgetTester tester) async {
      when(
        mockProvider.refreshNotifications(),
      ).thenThrow(Exception('Network error'));

      await tester.pumpWidget(createTestWidget());

      // Pull to refresh (should handle error)
      await tester.drag(find.byType(RefreshIndicator), const Offset(0, 300));
      await tester.pumpAndSettle();

      // Should show error snackbar
      expect(find.byType(SnackBar), findsOneWidget);
    });

    testWidgets('loads initial notifications on startup', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(notifications: []));

      // Wait for post frame callback
      await tester.pumpAndSettle();

      verify(mockProvider.loadNotifications(refresh: true)).called(1);
    });

    testWidgets('displays "You\'re all caught up" when no more notifications', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification()];

      await tester.pumpWidget(
        createTestWidget(
          notifications: notifications,
          hasMoreNotifications: false,
        ),
      );

      // Scroll to bottom
      await tester.drag(find.byType(ListView), const Offset(0, -500));
      await tester.pumpAndSettle();

      expect(find.text('You\'re all caught up!'), findsOneWidget);
    });

    testWidgets('back button navigates back', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // In a real app, this would navigate back
      // Here we just verify the button exists and is tappable
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });
  });
}
