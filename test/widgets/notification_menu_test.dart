import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';

import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/notification_menu.dart';

import 'notification_menu_test.mocks.dart';

@GenerateMocks([RealtimeNotificationProvider])
void main() {
  group('NotificationMenu Widget Tests', () {
    late MockRealtimeNotificationProvider mockProvider;

    setUp(() {
      mockProvider = MockRealtimeNotificationProvider();
    });

    Widget createTestWidget({
      Function(RealtimeNotificationModel)? onNotificationTap,
      List<RealtimeNotificationModel>? notifications,
      int unreadCount = 0,
      bool isLoading = false,
      bool hasMoreNotifications = true,
    }) {
      // Setup mock provider
      when(
        mockProvider.notifications,
      ).thenReturn(notifications ?? <RealtimeNotificationModel>[]);
      when(mockProvider.unreadCount).thenReturn(unreadCount);
      when(mockProvider.hasUnreadNotifications).thenReturn(unreadCount > 0);
      when(mockProvider.isLoading).thenReturn(isLoading);
      when(mockProvider.hasMoreNotifications).thenReturn(hasMoreNotifications);
      when(mockProvider.error).thenReturn(null);
      when(mockProvider.hasError).thenReturn(false);

      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          appBar: AppBar(
            actions: [
              ChangeNotifierProvider<RealtimeNotificationProvider>(
                create: (_) => mockProvider,
                child: NotificationMenu(onNotificationTap: onNotificationTap),
              ),
            ],
          ),
        ),
      );
    }

    RealtimeNotificationModel createMockNotification({
      String id = 'test-id',
      String title = 'Test Notification',
      String body = 'Test notification body',
      bool isRead = false,
      NotificationType type = NotificationType.comment,
      String actorUserId = 'actor-id',
      String? actorUsername = 'testuser',
      String? actorDisplayName = 'Test User',
    }) {
      return RealtimeNotificationModel(
        id: id,
        userId: 'user-id',
        type: type,
        actorUserId: actorUserId,
        actorUsername: actorUsername,
        actorDisplayName: actorDisplayName,
        title: title,
        body: body,
        isRead: isRead,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        source: NotificationSource.websocket,
        receivedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRealtime: true,
      );
    }

    testWidgets('displays notification icon', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.notifications_outlined), findsOneWidget);
    });

    testWidgets('displays unread badge when there are unread notifications', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(unreadCount: 5));

      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('does not display badge when no unread notifications', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(unreadCount: 0));

      expect(find.text('0'), findsNothing);
    });

    testWidgets('displays 99+ for unread count over 99', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(unreadCount: 150));

      expect(find.text('99+'), findsOneWidget);
    });

    testWidgets('opens dropdown menu when icon is tapped', (
      WidgetTester tester,
    ) async {
      final notifications = [
        createMockNotification(id: '1', title: 'Notification 1'),
        createMockNotification(id: '2', title: 'Notification 2'),
      ];

      await tester.pumpWidget(createTestWidget(notifications: notifications));

      // Tap the notification icon
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Check that dropdown is displayed
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Notification 1'), findsOneWidget);
      expect(find.text('Notification 2'), findsOneWidget);
    });

    testWidgets('closes dropdown when backdrop is tapped', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification()];

      await tester.pumpWidget(createTestWidget(notifications: notifications));

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      expect(find.text('Notifications'), findsOneWidget);

      // Tap backdrop to close
      await tester.tapAt(const Offset(50, 50)); // Tap outside dropdown
      await tester.pumpAndSettle();

      expect(find.text('Notifications'), findsNothing);
    });

    testWidgets('displays empty state when no notifications', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(notifications: []));

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      expect(find.text('No notifications yet'), findsOneWidget);
      expect(
        find.text('You\'ll see notifications here when you have new activity'),
        findsOneWidget,
      );
    });

    testWidgets('displays loading indicator when loading', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(notifications: [], isLoading: true),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets(
      'displays mark all read button when there are unread notifications',
      (WidgetTester tester) async {
        final notifications = [
          createMockNotification(id: '1', isRead: false),
          createMockNotification(id: '2', isRead: true),
        ];

        await tester.pumpWidget(
          createTestWidget(notifications: notifications, unreadCount: 1),
        );

        // Open dropdown
        await tester.tap(find.byIcon(Icons.notifications_outlined));
        await tester.pumpAndSettle();

        expect(find.text('Mark all read'), findsOneWidget);
      },
    );

    testWidgets(
      'does not display mark all read button when no unread notifications',
      (WidgetTester tester) async {
        final notifications = [
          createMockNotification(id: '1', isRead: true),
          createMockNotification(id: '2', isRead: true),
        ];

        await tester.pumpWidget(
          createTestWidget(notifications: notifications, unreadCount: 0),
        );

        // Open dropdown
        await tester.tap(find.byIcon(Icons.notifications_outlined));
        await tester.pumpAndSettle();

        expect(find.text('Mark all read'), findsNothing);
      },
    );

    testWidgets('calls markAllAsRead when mark all read button is tapped', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification(isRead: false)];

      when(mockProvider.markAllAsRead()).thenAnswer((_) async {});

      await tester.pumpWidget(
        createTestWidget(notifications: notifications, unreadCount: 1),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Tap mark all read
      await tester.tap(find.text('Mark all read'));
      await tester.pumpAndSettle();

      verify(mockProvider.markAllAsRead()).called(1);
    });

    testWidgets('calls onNotificationTap when notification is tapped', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification();
      RealtimeNotificationModel? tappedNotification;

      await tester.pumpWidget(
        createTestWidget(
          notifications: [notification],
          onNotificationTap: (n) => tappedNotification = n,
        ),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Tap notification
      await tester.tap(find.text('Test notification body'));
      await tester.pumpAndSettle();

      expect(tappedNotification, equals(notification));
    });

    testWidgets('displays pagination loader when has more notifications', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification()];

      await tester.pumpWidget(
        createTestWidget(
          notifications: notifications,
          hasMoreNotifications: true,
        ),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Scroll to bottom to trigger pagination
      await tester.drag(find.byType(ListView), const Offset(0, -500));
      await tester.pumpAndSettle();

      expect(find.text('Load more'), findsOneWidget);
    });

    testWidgets('calls loadMoreNotifications when scrolled to bottom', (
      WidgetTester tester,
    ) async {
      final notifications = List.generate(
        10,
        (i) => createMockNotification(id: 'id-$i'),
      );

      when(mockProvider.loadMoreNotifications()).thenAnswer((_) async {});

      await tester.pumpWidget(
        createTestWidget(
          notifications: notifications,
          hasMoreNotifications: true,
        ),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Scroll to bottom
      await tester.drag(find.byType(ListView), const Offset(0, -1000));
      await tester.pumpAndSettle();

      // Wait for debounce timer
      await tester.pump(const Duration(milliseconds: 300));

      verify(mockProvider.loadMoreNotifications()).called(1);
    });

    testWidgets('displays refresh indicator and calls refreshNotifications', (
      WidgetTester tester,
    ) async {
      final notifications = [createMockNotification()];

      when(mockProvider.refreshNotifications()).thenAnswer((_) async {});

      await tester.pumpWidget(createTestWidget(notifications: notifications));

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Pull to refresh
      await tester.drag(find.byType(RefreshIndicator), const Offset(0, 300));
      await tester.pumpAndSettle();

      verify(mockProvider.refreshNotifications()).called(1);
    });

    testWidgets('handles error states gracefully', (WidgetTester tester) async {
      when(mockProvider.markAllAsRead()).thenThrow(Exception('Network error'));

      final notifications = [createMockNotification(isRead: false)];

      await tester.pumpWidget(
        createTestWidget(notifications: notifications, unreadCount: 1),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Tap mark all read (should handle error)
      await tester.tap(find.text('Mark all read'));
      await tester.pumpAndSettle();

      // Should show error snackbar
      expect(find.byType(SnackBar), findsOneWidget);
    });

    testWidgets('updates badge count when notifications change', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(unreadCount: 3));

      expect(find.text('3'), findsOneWidget);

      // Update mock to return different count
      when(mockProvider.unreadCount).thenReturn(5);
      when(mockProvider.hasUnreadNotifications).thenReturn(true);

      // Trigger rebuild
      await tester.pump();

      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('positions dropdown correctly on screen', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(notifications: [createMockNotification()]),
      );

      // Open dropdown
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Check that dropdown is positioned and visible
      final dropdown = find.byType(Material).last;
      expect(dropdown, findsOneWidget);

      final dropdownWidget = tester.widget<Material>(dropdown);
      expect(dropdownWidget.elevation, equals(8));
    });
  });
}
