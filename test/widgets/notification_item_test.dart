import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/notification_item.dart';

void main() {
  group('NotificationItem Widget Tests', () {
    Widget createTestWidget({
      required RealtimeNotificationModel notification,
      VoidCallback? onTap,
      VoidCallback? onMarkAsRead,
      bool showDivider = true,
    }) {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          body: NotificationItem(
            notification: notification,
            onTap: onTap,
            onMarkAsRead: onMarkAsRead,
            showDivider: showDivider,
          ),
        ),
      );
    }

    RealtimeNotificationModel createMockNotification({
      String id = 'test-id',
      String title = 'Test Notification',
      String body = 'Test notification body',
      bool isRead = false,
      NotificationType type = NotificationType.comment,
      String actorUserId = 'actor-id',
      String? actorUsername = 'testuser',
      String? actorDisplayName = 'Test User',
      String? actorAvatarUrl,
      DateTime? createdAt,
      bool isRealtime = true,
    }) {
      return RealtimeNotificationModel(
        id: id,
        userId: 'user-id',
        type: type,
        actorUserId: actorUserId,
        actorUsername: actorUsername,
        actorDisplayName: actorDisplayName,
        actorAvatarUrl: actorAvatarUrl,
        title: title,
        body: body,
        isRead: isRead,
        createdAt:
            createdAt ?? DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt:
            createdAt ?? DateTime.now().subtract(const Duration(hours: 1)),
        source: NotificationSource.websocket,
        receivedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRealtime: isRealtime,
      );
    }

    testWidgets('displays notification content correctly', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(
        actorDisplayName: 'John Doe',
        body: 'This is a test notification',
      );

      await tester.pumpWidget(createTestWidget(notification: notification));

      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('This is a test notification'), findsOneWidget);
    });

    testWidgets(
      'displays correct action text for different notification types',
      (WidgetTester tester) async {
        // Test comment notification
        final commentNotification = createMockNotification(
          type: NotificationType.comment,
          actorDisplayName: 'John',
        );

        await tester.pumpWidget(
          createTestWidget(notification: commentNotification),
        );
        expect(find.textContaining('commented on your post'), findsOneWidget);

        // Test follow notification
        final followNotification = createMockNotification(
          type: NotificationType.follow,
          actorDisplayName: 'Jane',
        );

        await tester.pumpWidget(
          createTestWidget(notification: followNotification),
        );
        expect(find.textContaining('started following you'), findsOneWidget);

        // Test reflex notification
        final reflexNotification = createMockNotification(
          type: NotificationType.reflex,
          actorDisplayName: 'Bob',
        );

        await tester.pumpWidget(
          createTestWidget(notification: reflexNotification),
        );
        expect(
          find.textContaining('added a reflex to your post'),
          findsOneWidget,
        );
      },
    );

    testWidgets('displays correct icons for different notification types', (
      WidgetTester tester,
    ) async {
      // Test comment notification
      final commentNotification = createMockNotification(
        type: NotificationType.comment,
      );
      await tester.pumpWidget(
        createTestWidget(notification: commentNotification),
      );
      expect(find.byIcon(Icons.comment), findsOneWidget);

      // Test follow notification
      final followNotification = createMockNotification(
        type: NotificationType.follow,
      );
      await tester.pumpWidget(
        createTestWidget(notification: followNotification),
      );
      expect(find.byIcon(Icons.person_add), findsOneWidget);

      // Test reflex notification
      final reflexNotification = createMockNotification(
        type: NotificationType.reflex,
      );
      await tester.pumpWidget(
        createTestWidget(notification: reflexNotification),
      );
      expect(find.byIcon(Icons.flash_on), findsOneWidget);
    });

    testWidgets('shows unread indicator for unread notifications', (
      WidgetTester tester,
    ) async {
      final unreadNotification = createMockNotification(isRead: false);

      await tester.pumpWidget(
        createTestWidget(notification: unreadNotification),
      );

      // Find the unread indicator (small green circle)
      final unreadIndicators = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).color == AppColors.gfGreen &&
            (widget.decoration as BoxDecoration).shape == BoxShape.circle,
      );

      expect(unreadIndicators, findsWidgets);
    });

    testWidgets('does not show unread indicator for read notifications', (
      WidgetTester tester,
    ) async {
      final readNotification = createMockNotification(isRead: true);

      await tester.pumpWidget(createTestWidget(notification: readNotification));

      // Should not find unread indicator in trailing area
      final trailingArea = find.byWidgetPredicate(
        (widget) => widget is Column && widget.children.length >= 2,
      );

      expect(trailingArea, findsOneWidget);
    });

    testWidgets('shows real-time indicator for real-time notifications', (
      WidgetTester tester,
    ) async {
      final realtimeNotification = createMockNotification(isRealtime: true);

      await tester.pumpWidget(
        createTestWidget(notification: realtimeNotification),
      );

      // Find the real-time indicator (small green dot in metadata)
      final realtimeIndicators = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).color == AppColors.gfGreen &&
            (widget.decoration as BoxDecoration).shape == BoxShape.circle,
      );

      expect(realtimeIndicators, findsWidgets);
    });

    testWidgets('calls onTap when notification is tapped', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification();
      bool wasTapped = false;

      await tester.pumpWidget(
        createTestWidget(
          notification: notification,
          onTap: () => wasTapped = true,
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      expect(wasTapped, isTrue);
    });

    testWidgets('calls onMarkAsRead when unread notification is tapped', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(isRead: false);
      bool wasMarkedAsRead = false;

      await tester.pumpWidget(
        createTestWidget(
          notification: notification,
          onMarkAsRead: () => wasMarkedAsRead = true,
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      expect(wasMarkedAsRead, isTrue);
    });

    testWidgets('does not call onMarkAsRead when read notification is tapped', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(isRead: true);
      bool wasMarkedAsRead = false;

      await tester.pumpWidget(
        createTestWidget(
          notification: notification,
          onMarkAsRead: () => wasMarkedAsRead = true,
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      expect(wasMarkedAsRead, isFalse);
    });

    testWidgets('displays popup menu with correct options', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(isRead: false);

      await tester.pumpWidget(createTestWidget(notification: notification));

      // Tap the more menu button
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      expect(find.text('Mark as read'), findsOneWidget);
      expect(find.text('View post'), findsOneWidget);
    });

    testWidgets('popup menu shows correct view option for different types', (
      WidgetTester tester,
    ) async {
      // Test follow notification
      final followNotification = createMockNotification(
        type: NotificationType.follow,
      );
      await tester.pumpWidget(
        createTestWidget(notification: followNotification),
      );

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      expect(find.text('View profile'), findsOneWidget);
    });

    testWidgets('does not show mark as read option for read notifications', (
      WidgetTester tester,
    ) async {
      final readNotification = createMockNotification(isRead: true);

      await tester.pumpWidget(createTestWidget(notification: readNotification));

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      expect(find.text('Mark as read'), findsNothing);
    });

    testWidgets('calls onMarkAsRead when mark as read menu item is tapped', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(isRead: false);
      bool wasMarkedAsRead = false;

      await tester.pumpWidget(
        createTestWidget(
          notification: notification,
          onMarkAsRead: () => wasMarkedAsRead = true,
        ),
      );

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Mark as read'));
      await tester.pumpAndSettle();

      expect(wasMarkedAsRead, isTrue);
    });

    testWidgets('displays divider when showDivider is true', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification();

      await tester.pumpWidget(
        createTestWidget(notification: notification, showDivider: true),
      );

      expect(find.byType(Divider), findsOneWidget);
    });

    testWidgets('does not display divider when showDivider is false', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification();

      await tester.pumpWidget(
        createTestWidget(notification: notification, showDivider: false),
      );

      expect(find.byType(Divider), findsNothing);
    });

    testWidgets('displays avatar with correct fallback', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(
        actorDisplayName: 'John Doe',
        actorAvatarUrl: null, // No avatar URL
      );

      await tester.pumpWidget(createTestWidget(notification: notification));

      // Should display avatar with initials
      expect(find.text('JD'), findsOneWidget);
    });

    testWidgets('handles long notification text with ellipsis', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(
        body:
            'This is a very long notification body that should be truncated with ellipsis when it exceeds the maximum number of lines allowed for display in the notification item widget',
      );

      await tester.pumpWidget(createTestWidget(notification: notification));

      // Find the text widget and verify it has maxLines set
      final textWidget = tester.widget<Text>(
        find.textContaining('This is a very long notification'),
      );

      expect(textWidget.maxLines, equals(2));
      expect(textWidget.overflow, equals(TextOverflow.ellipsis));
    });

    testWidgets('displays relative timestamp', (WidgetTester tester) async {
      final notification = createMockNotification(
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      );

      await tester.pumpWidget(createTestWidget(notification: notification));

      // Should display relative time like "2 hours ago"
      expect(find.textContaining('ago'), findsOneWidget);
    });

    testWidgets('applies correct styling for read vs unread notifications', (
      WidgetTester tester,
    ) async {
      // Test unread notification
      final unreadNotification = createMockNotification(isRead: false);
      await tester.pumpWidget(
        createTestWidget(notification: unreadNotification),
      );

      // Should have highlighted background
      final unreadContainer = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).color != null,
      );
      expect(unreadContainer, findsOneWidget);

      // Test read notification
      final readNotification = createMockNotification(isRead: true);
      await tester.pumpWidget(createTestWidget(notification: readNotification));

      // Should have transparent background
      final readContainer = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).color == Colors.transparent,
      );
      expect(readContainer, findsOneWidget);
    });
  });

  group('CompactNotificationItem Widget Tests', () {
    Widget createTestWidget({
      required RealtimeNotificationModel notification,
      VoidCallback? onTap,
    }) {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          body: CompactNotificationItem(
            notification: notification,
            onTap: onTap,
          ),
        ),
      );
    }

    RealtimeNotificationModel createMockNotification({
      String id = 'test-id',
      String title = 'Test Notification',
      bool isRead = false,
      String? actorDisplayName = 'Test User',
    }) {
      return RealtimeNotificationModel(
        id: id,
        userId: 'user-id',
        type: NotificationType.comment,
        actorUserId: 'actor-id',
        actorDisplayName: actorDisplayName,
        title: title,
        body: 'Test body',
        isRead: isRead,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        source: NotificationSource.websocket,
        receivedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRealtime: true,
      );
    }

    testWidgets('displays compact notification content', (
      WidgetTester tester,
    ) async {
      final notification = createMockNotification(title: 'Compact Test');

      await tester.pumpWidget(createTestWidget(notification: notification));

      expect(find.text('Compact Test'), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      final notification = createMockNotification();
      bool wasTapped = false;

      await tester.pumpWidget(
        createTestWidget(
          notification: notification,
          onTap: () => wasTapped = true,
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      expect(wasTapped, isTrue);
    });

    testWidgets('shows unread indicator for unread notifications', (
      WidgetTester tester,
    ) async {
      final unreadNotification = createMockNotification(isRead: false);

      await tester.pumpWidget(
        createTestWidget(notification: unreadNotification),
      );

      final unreadIndicators = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).color == AppColors.gfGreen &&
            (widget.decoration as BoxDecoration).shape == BoxShape.circle,
      );

      expect(unreadIndicators, findsOneWidget);
    });
  });

  group('TypedNotificationItem Widget Tests', () {
    Widget createTestWidget({
      required RealtimeNotificationModel notification,
      VoidCallback? onTap,
      VoidCallback? onMarkAsRead,
    }) {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          body: TypedNotificationItem(
            notification: notification,
            onTap: onTap,
            onMarkAsRead: onMarkAsRead,
          ),
        ),
      );
    }

    RealtimeNotificationModel createMockNotification({
      NotificationType type = NotificationType.comment,
    }) {
      return RealtimeNotificationModel(
        id: 'test-id',
        userId: 'user-id',
        type: type,
        actorUserId: 'actor-id',
        actorDisplayName: 'Test User',
        title: 'Test Notification',
        body: 'Test body',
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        source: NotificationSource.websocket,
        receivedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRealtime: true,
      );
    }

    testWidgets('renders different types correctly', (
      WidgetTester tester,
    ) async {
      for (final type in NotificationType.values) {
        final notification = createMockNotification(type: type);

        await tester.pumpWidget(createTestWidget(notification: notification));

        // Should render as NotificationItem for all types
        expect(find.byType(NotificationItem), findsOneWidget);
      }
    });
  });
}
