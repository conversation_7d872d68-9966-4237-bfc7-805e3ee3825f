import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';
import 'package:gameflex_mobile/widgets/notification_menu.dart';
// import 'package:gameflex_mobile/models/notification_model.dart'; // Unused import

void main() {
  group('WebSocket Integration Tests', () {
    testWidgets('Notification menu displays correctly', (
      WidgetTester tester,
    ) async {
      // Create a fresh provider instance for testing
      final notificationProvider = RealtimeNotificationProvider.instance;

      // Clear any existing notifications
      notificationProvider.clearAllNotifications();

      // Create test app with providers
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<RealtimeNotificationProvider>.value(
            value: notificationProvider,
            child: Scaffold(
              appBar: AppBar(actions: [NotificationMenu()]),
              body: Container(),
            ),
          ),
        ),
      );

      // Verify initial state - notification icon should be present
      expect(find.byIcon(Icons.notifications_outlined), findsOneWidget);

      // Tap notification menu to open it
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Should show empty state initially
      expect(find.text('No notifications yet'), findsOneWidget);
      expect(find.text('Notifications'), findsOneWidget);
    });

    testWidgets('Notification menu header displays correctly', (
      WidgetTester tester,
    ) async {
      final notificationProvider = RealtimeNotificationProvider.instance;
      notificationProvider.clearAllNotifications();

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<RealtimeNotificationProvider>.value(
            value: notificationProvider,
            child: Scaffold(
              appBar: AppBar(actions: [NotificationMenu()]),
              body: Container(),
            ),
          ),
        ),
      );

      // Open notification menu
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Verify header elements
      expect(find.text('Notifications'), findsOneWidget);

      // Should not show "Mark all read" button when no unread notifications
      expect(find.text('Mark all read'), findsNothing);
    });

    testWidgets('Notification menu closes when tapped outside', (
      WidgetTester tester,
    ) async {
      final notificationProvider = RealtimeNotificationProvider.instance;
      notificationProvider.clearAllNotifications();

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<RealtimeNotificationProvider>.value(
            value: notificationProvider,
            child: Scaffold(
              appBar: AppBar(actions: [NotificationMenu()]),
              body: Text('Main Content'),
            ),
          ),
        ),
      );

      // Open notification menu
      await tester.tap(find.byIcon(Icons.notifications_outlined));
      await tester.pumpAndSettle();

      // Verify menu is open
      expect(find.text('Notifications'), findsOneWidget);

      // Tap outside the menu (on main content)
      await tester.tap(find.text('Main Content'));
      await tester.pumpAndSettle();

      // Menu should be closed (notifications header should not be visible)
      expect(find.text('Notifications'), findsNothing);
    });
  });
}
