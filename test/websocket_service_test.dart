import 'dart:async';
// import 'dart:convert'; // Unused import

import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/services/websocket_service.dart';

void main() {
  group('WebSocketService', () {
    late WebSocketService webSocketService;

    setUp(() {
      webSocketService = WebSocketService.instance;
      // Clear any handlers from previous tests to ensure isolation
      webSocketService.clearAllHandlers();
    });

    group('Singleton Pattern', () {
      test('should return the same instance', () {
        final instance1 = WebSocketService.instance;
        final instance2 = WebSocketService.instance;
        expect(instance1, same(instance2));
      });
    });

    group('Initial State', () {
      test('should start with disconnected state', () {
        expect(webSocketService.connectionState, ConnectionState.disconnected);
        expect(webSocketService.isConnected, false);
      });

      test('should provide message streams', () {
        expect(webSocketService.messageStream, isA<Stream<WebSocketMessage>>());
        expect(
          webSocketService.notificationStream,
          isA<Stream<NotificationModel>>(),
        );
        expect(
          webSocketService.postUpdateStream,
          isA<Stream<Map<String, dynamic>>>(),
        );
        expect(
          webSocketService.commentUpdateStream,
          isA<Stream<Map<String, dynamic>>>(),
        );
        expect(
          webSocketService.connectionStateStream,
          isA<Stream<ConnectionState>>(),
        );
      });
    });

    group('Message Parsing', () {
      test('should parse WebSocketMessage from JSON correctly', () {
        final timestamp = DateTime.now();
        final messageJson = {
          'type': 'notification',
          'timestamp': timestamp.toIso8601String(),
          'data': {'id': 'test-notification'},
        };

        final message = WebSocketMessage.fromJson(messageJson);

        expect(message.type, WebSocketMessageType.notification);
        expect(message.data['id'], 'test-notification');
        expect(
          message.timestamp.millisecondsSinceEpoch,
          timestamp.millisecondsSinceEpoch,
        );
      });

      test('should parse different message types correctly', () {
        final testCases = [
          ('notification', WebSocketMessageType.notification),
          ('post_update', WebSocketMessageType.postUpdate),
          ('comment_update', WebSocketMessageType.commentUpdate),
          ('ping', WebSocketMessageType.ping),
          ('pong', WebSocketMessageType.pong),
          ('error', WebSocketMessageType.error),
          ('ack', WebSocketMessageType.ack),
          ('system', WebSocketMessageType.system),
          ('unknown', WebSocketMessageType.system), // defaults to system
        ];

        for (final testCase in testCases) {
          final typeString = testCase.$1;
          final expectedType = testCase.$2;

          final messageJson = <String, dynamic>{
            'type': typeString,
            'timestamp': DateTime.now().toIso8601String(),
            'data': <String, dynamic>{},
          };

          final message = WebSocketMessage.fromJson(messageJson);
          expect(
            message.type,
            expectedType,
            reason: 'Failed for type: $typeString',
          );
        }
      });

      test('should convert WebSocketMessage to JSON correctly', () {
        final timestamp = DateTime.now();
        final message = WebSocketMessage(
          type: WebSocketMessageType.notification,
          data: {'id': 'test', 'value': 123},
          timestamp: timestamp,
        );

        final json = message.toJson();

        expect(json['type'], 'notification');
        expect(json['data']['id'], 'test');
        expect(json['data']['value'], 123);
        expect(json['timestamp'], timestamp.toIso8601String());
      });
    });

    group('Message Handlers', () {
      test('should register and track message handlers', () {
        // var handlerCalled = false; // Unused variable
        // WebSocketMessage? receivedMessage; // Unused variable

        void testHandler(WebSocketMessage message) {
          // handlerCalled = true; // Unused variable
          // receivedMessage = message; // Unused variable
        }

        webSocketService.registerMessageHandler(
          WebSocketMessageType.notification,
          testHandler,
        );

        final debugInfo = webSocketService.getDebugInfo();
        final handlers =
            debugInfo['registeredHandlers'] as Map<String, dynamic>;
        expect(handlers['notification'], 1);
      });

      test('should unregister message handlers', () {
        void testHandler(WebSocketMessage message) {}

        webSocketService.registerMessageHandler(
          WebSocketMessageType.notification,
          testHandler,
        );

        webSocketService.unregisterMessageHandler(
          WebSocketMessageType.notification,
          testHandler,
        );

        final debugInfo = webSocketService.getDebugInfo();
        final handlers =
            debugInfo['registeredHandlers'] as Map<String, dynamic>;
        expect(handlers['notification'] ?? 0, 0);
      });

      test('should handle multiple handlers for same message type', () {
        void handler1(WebSocketMessage message) {}
        void handler2(WebSocketMessage message) {}

        webSocketService.registerMessageHandler(
          WebSocketMessageType.notification,
          handler1,
        );
        webSocketService.registerMessageHandler(
          WebSocketMessageType.notification,
          handler2,
        );

        final debugInfo = webSocketService.getDebugInfo();
        final handlers =
            debugInfo['registeredHandlers'] as Map<String, dynamic>;
        expect(handlers['notification'], 2);
      });
    });

    group('App Lifecycle Management', () {
      test('should handle app going to background', () {
        // Should not throw exception
        expect(() => webSocketService.onAppBackground(), returnsNormally);
      });

      test('should handle app coming to foreground', () {
        // Should not throw exception
        expect(() => webSocketService.onAppForeground(), returnsNormally);
      });
    });

    group('Subscription Management', () {
      test('should handle post subscription without connection', () async {
        // Should not throw exception when not connected
        expect(
          () => webSocketService.subscribeToPost('test-post-id'),
          returnsNormally,
        );
      });

      test('should handle post unsubscription without connection', () async {
        // Should not throw exception when not connected
        expect(
          () => webSocketService.unsubscribeFromPost('test-post-id'),
          returnsNormally,
        );
      });

      test('should handle ping without connection', () async {
        // Should not throw exception when not connected
        expect(() => webSocketService.sendPing(), returnsNormally);
      });
    });

    group('Debug Information', () {
      test('should provide comprehensive debug information', () {
        final debugInfo = webSocketService.getDebugInfo();

        expect(debugInfo, isA<Map<String, dynamic>>());
        expect(debugInfo.containsKey('connectionState'), true);
        expect(debugInfo.containsKey('isConnected'), true);
        expect(debugInfo.containsKey('reconnectAttempts'), true);
        expect(debugInfo.containsKey('hasWebSocket'), true);
        expect(debugInfo.containsKey('isAppInForeground'), true);
        expect(debugInfo.containsKey('hasValidToken'), true);
        expect(debugInfo.containsKey('isAuthenticated'), true);
        expect(debugInfo.containsKey('registeredHandlers'), true);
        expect(debugInfo.containsKey('streamControllers'), true);
      });

      test('should track stream controller states', () {
        final debugInfo = webSocketService.getDebugInfo();
        final streamControllers =
            debugInfo['streamControllers'] as Map<String, dynamic>;

        expect(streamControllers.containsKey('notifications'), true);
        expect(streamControllers.containsKey('postUpdates'), true);
        expect(streamControllers.containsKey('commentUpdates'), true);
      });
    });

    group('Connection Management', () {
      test('should handle connection without authentication', () async {
        // Should handle gracefully when not authenticated
        expect(() => webSocketService.connect(), returnsNormally);
      });

      test('should handle disconnect when not connected', () async {
        // Should handle gracefully when not connected
        expect(() => webSocketService.disconnect(), returnsNormally);
      });

      test('should emit connection state changes', () async {
        final states = <ConnectionState>[];
        late StreamSubscription subscription;

        // Listen to connection state changes
        subscription = webSocketService.connectionStateStream.listen(
          states.add,
        );

        // Attempt connection (will fail due to no auth, but should emit states)
        webSocketService.connect();

        // Give some time for state changes
        await Future.delayed(Duration(milliseconds: 50));

        // Clean up
        await subscription.cancel();

        // Should have received at least one state change
        expect(states.isNotEmpty, true);
      });
    });

    group('Message Sending', () {
      test('should handle sending message when not connected', () async {
        // Should throw exception when not connected
        expect(
          () => webSocketService.sendMessage({'test': 'message'}),
          throwsException,
        );
      });
    });

    group('Service Lifecycle', () {
      test('should initialize without errors', () async {
        // Should not throw exception
        expect(() => webSocketService.initialize(), returnsNormally);
      });

      test('should dispose without errors', () {
        // Should not throw exception
        expect(() => webSocketService.dispose(), returnsNormally);
      });

      test('should handle multiple dispose calls', () {
        webSocketService.dispose();
        // Second dispose should not throw
        expect(() => webSocketService.dispose(), returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle invalid message data gracefully', () {
        // Test sending message when not connected should throw
        expect(
          () => webSocketService.sendMessage({'test': 'message'}),
          throwsException,
        );
      });
    });

    group('Stream Behavior', () {
      test('should provide broadcast streams', () async {
        // Multiple listeners should be supported
        final listener1 = webSocketService.messageStream.listen((_) {});
        final listener2 = webSocketService.messageStream.listen((_) {});
        final listener3 = webSocketService.notificationStream.listen((_) {});
        final listener4 = webSocketService.postUpdateStream.listen((_) {});
        final listener5 = webSocketService.commentUpdateStream.listen((_) {});

        // Should not throw exception
        expect(() async {
          await listener1.cancel();
          await listener2.cancel();
          await listener3.cancel();
          await listener4.cancel();
          await listener5.cancel();
        }, returnsNormally);
      });
    });
  });
}
