// import 'dart:async'; // Unused import

import 'package:flutter_test/flutter_test.dart';

import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';

void main() {
  group('RealtimeNotificationProvider', () {
    late RealtimeNotificationProvider provider;

    setUp(() {
      // Create provider instance
      provider = RealtimeNotificationProvider.instance;
    });

    tearDown(() {
      // Clean up provider state
      provider.clearAllNotifications();
    });

    group('Basic State Management', () {
      test('should have correct initial state', () {
        // Verify initial state
        expect(provider.notifications, isEmpty);
        expect(provider.unreadCount, equals(0));
        expect(provider.hasUnreadNotifications, isFalse);
        expect(provider.isEmpty, isTrue);
        expect(provider.isNotEmpty, isFalse);
      });

      test('should clear all notifications', () {
        // Clear notifications (should work even if empty)
        provider.clearAllNotifications();

        // Verify cleared state
        expect(provider.notifications, isEmpty);
        expect(provider.unreadCount, equals(0));
        expect(provider.hasUnreadNotifications, isFalse);
      });
    });

    group('RealtimeNotificationModel', () {
      test('should create from NotificationModel correctly', () {
        // Create base notification
        final baseNotification = _createTestNotification(
          id: 'test-1',
          isRead: false,
        );

        // Create realtime notification
        final realtimeNotification = RealtimeNotificationModel.fromNotification(
          baseNotification,
          source: NotificationSource.websocket,
        );

        // Verify properties
        expect(realtimeNotification.id, equals('test-1'));
        expect(
          realtimeNotification.source,
          equals(NotificationSource.websocket),
        );
        expect(realtimeNotification.isRealtime, isTrue);
        expect(realtimeNotification.receivedAt, isNotNull);
      });

      test('should copy with new properties', () {
        // Create base notification
        final baseNotification = _createTestNotification(
          id: 'test-1',
          isRead: false,
        );

        final realtimeNotification = RealtimeNotificationModel.fromNotification(
          baseNotification,
          source: NotificationSource.api,
        );

        // Copy with read status changed
        final updatedNotification = realtimeNotification.copyWith(isRead: true);

        // Verify original is unchanged
        expect(realtimeNotification.isRead, isFalse);

        // Verify copy has new value
        expect(updatedNotification.isRead, isTrue);
        expect(updatedNotification.id, equals(realtimeNotification.id));
      });
    });

    group('Utility Methods', () {
      test('should filter notifications by type', () {
        // Create test notifications with different types
        // final followNotification = RealtimeNotificationModel.fromNotification(
        //   _createTestNotification(
        //     id: 'follow-1',
        //     type: NotificationType.follow,
        //   ),
        //   source: NotificationSource.api,
        // ); // Unused variable

        // final commentNotification = RealtimeNotificationModel.fromNotification(
        //   _createTestNotification(
        //     id: 'comment-1',
        //     type: NotificationType.comment,
        //   ),
        //   source: NotificationSource.api,
        // ); // Unused variable

        // Manually add notifications to test filtering
        // Note: In a real test with dependency injection, we'd use proper mocking

        // Test filtering by type (using empty provider for now)
        final followNotifications = provider.getNotificationsByType(
          NotificationType.follow,
        );
        final commentNotifications = provider.getNotificationsByType(
          NotificationType.comment,
        );

        // Verify filtering works (will be empty since no notifications added)
        expect(followNotifications, isEmpty);
        expect(commentNotifications, isEmpty);
      });

      test('should check if notification exists', () {
        // Test existence check on empty provider
        expect(provider.hasNotification('exists-1'), isFalse);
        expect(provider.hasNotification('not-exists'), isFalse);
      });

      test('should get notification by ID', () {
        // Test getting by ID on empty provider
        final found = provider.getNotificationById('find-me');
        expect(found, isNull);

        final notFound = provider.getNotificationById('not-exists');
        expect(notFound, isNull);
      });

      test('should get notification statistics', () {
        // Get statistics from empty provider
        final stats = provider.getNotificationStats();

        // Verify statistics structure
        expect(stats['total'], equals(0));
        expect(stats['unread'], equals(0));
        expect(stats['read'], equals(0));
        expect(stats.containsKey('follow_total'), isTrue);
        expect(stats.containsKey('comment_total'), isTrue);
        expect(stats.containsKey('reflex_total'), isTrue);
      });

      test('should get debug information', () {
        // Get debug info
        final debugInfo = provider.getDebugInfo();

        // Verify debug info structure
        expect(debugInfo.containsKey('totalNotifications'), isTrue);
        expect(debugInfo.containsKey('unreadCount'), isTrue);
        expect(debugInfo.containsKey('isLoading'), isTrue);
        expect(debugInfo.containsKey('hasMoreNotifications'), isTrue);
        expect(debugInfo.containsKey('notificationsBySource'), isTrue);
        expect(debugInfo.containsKey('notificationsByType'), isTrue);
      });
    });

    group('Notification Source Enum', () {
      test('should have correct notification sources', () {
        expect(NotificationSource.values.length, equals(3));
        expect(
          NotificationSource.values,
          contains(NotificationSource.websocket),
        );
        expect(NotificationSource.values, contains(NotificationSource.api));
        expect(NotificationSource.values, contains(NotificationSource.local));
      });
    });
  });
}

/// Helper function to create test notifications
NotificationModel _createTestNotification({
  required String id,
  NotificationType type = NotificationType.comment,
  bool isRead = false,
  String? title,
  String? body,
}) {
  return NotificationModel(
    id: id,
    userId: 'test-user-id',
    type: type,
    actorUserId: 'actor-user-id',
    actorUsername: 'actor_username',
    actorDisplayName: 'Actor Display Name',
    actorAvatarUrl: 'https://example.com/avatar.jpg',
    title: title ?? 'Test Notification',
    body: body ?? 'This is a test notification',
    data: {'postId': 'test-post-id', 'commentId': 'test-comment-id'},
    isRead: isRead,
    createdAt: DateTime.now().subtract(Duration(minutes: 5)),
    updatedAt: DateTime.now(),
  );
}
